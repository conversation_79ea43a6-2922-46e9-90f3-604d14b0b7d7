/*
 * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
 * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
*/

using System;
using QuantConnect.Data;
using QuantConnect.Data.Consolidators;
using QuantConnect.Data.Market;
using ATR_Evolv_3.Models.Utils;

namespace QuantConnect.Algorithm.CSharp
{
    /// <summary>
    /// A wrapper around any consolidator that logs all method calls for debugging purposes.
    /// This helps track exactly what data is being sent to a consolidator and when.
    /// </summary>
    public class DebugConsolidatorWrapper : IDataConsolidator
    {
        private readonly IDataConsolidator _wrappedConsolidator;
        private readonly string _name;

        /// <summary>
        /// Gets the most recently consolidated piece of data. This will be null if this consolidator
        /// has not produced any data yet.
        /// </summary>
        public IBaseData Consolidated => _wrappedConsolidator.Consolidated;

        /// <summary>
        /// Gets a clone of the data being currently consolidated
        /// </summary>
        public IBaseData WorkingData => _wrappedConsolidator.WorkingData;

        /// <summary>
        /// Gets the type consumed by this consolidator
        /// </summary>
        public Type InputType => _wrappedConsolidator.InputType;

        /// <summary>
        /// Gets the type produced by this consolidator
        /// </summary>
        public Type OutputType => _wrappedConsolidator.OutputType;

        /// <summary>
        /// Event handler that fires when a new piece of data is produced
        /// </summary>
        public event DataConsolidatedHandler DataConsolidated;

        /// <summary>
        /// Initializes a new instance of the <see cref="DebugConsolidatorWrapper"/> class
        /// </summary>
        /// <param name="consolidator">The consolidator to wrap</param>
        /// <param name="name">A name for this wrapper for logging purposes</param>
        public DebugConsolidatorWrapper(IDataConsolidator consolidator, string name)
        {
            _wrappedConsolidator = consolidator ?? throw new ArgumentNullException(nameof(consolidator));
            _name = name ?? "Unknown";

            LogManager.Log($"[DEBUG-WRAPPER-{_name}] Created wrapper around {_wrappedConsolidator.GetType().Name}");

            // Wire up the wrapped consolidator's event to our event
            _wrappedConsolidator.DataConsolidated += OnWrappedConsolidatorDataConsolidated;
        }

        /// <summary>
        /// Updates this consolidator with the specified data
        /// </summary>
        /// <param name="data">The new data for the consolidator</param>
        public void Update(IBaseData data)
        {
            LogManager.Log($"[DEBUG-WRAPPER-{_name}] Update() called with {data?.GetType().Name} for {data?.Symbol} at {data?.Time}");
            LogManager.Log($"[DEBUG-WRAPPER-{_name}] Data details: Close={GetClosePrice(data):F2}, Volume={GetVolume(data):F0}");
            
            // Call the wrapped consolidator
            _wrappedConsolidator.Update(data);
            
            LogManager.Log($"[DEBUG-WRAPPER-{_name}] Update() completed");
        }

        /// <summary>
        /// Scans this consolidator to see if it should emit a bar due to time passing
        /// </summary>
        /// <param name="currentLocalTime">The current time in the local time zone (same as <see cref="BaseData.Time"/>)</param>
        public void Scan(DateTime currentLocalTime)
        {
            LogManager.LogThrottled($"[DEBUG-WRAPPER-{_name}] Scan() called with time {currentLocalTime}", TimeSpan.FromMinutes(10));
            _wrappedConsolidator.Scan(currentLocalTime);
        }

        /// <summary>
        /// Resets this consolidator to its initial state
        /// </summary>
        public void Reset()
        {
            LogManager.Log($"[DEBUG-WRAPPER-{_name}] Reset() called");
            _wrappedConsolidator.Reset();
        }

        /// <summary>
        /// Event handler for when the wrapped consolidator produces data
        /// </summary>
        private void OnWrappedConsolidatorDataConsolidated(object sender, IBaseData consolidated)
        {
            LogManager.Log($"[DEBUG-WRAPPER-{_name}] Wrapped consolidator produced data: {consolidated?.GetType().Name} for {consolidated?.Symbol} at {consolidated?.Time}");
            LogManager.Log($"[DEBUG-WRAPPER-{_name}] Output details: Close={GetClosePrice(consolidated):F2}, Volume={GetVolume(consolidated):F0}");
            
            // Fire our own event
            DataConsolidated?.Invoke(this, consolidated);
        }

        /// <summary>
        /// Helper method to safely get close price from various data types
        /// </summary>
        private decimal GetClosePrice(IBaseData data)
        {
            return data switch
            {
                TradeBar tradeBar => tradeBar.Close,
                QuoteBar quoteBar => quoteBar.Close,
                Tick tick => tick.Value,
                _ => 0m
            };
        }

        /// <summary>
        /// Helper method to safely get volume from various data types
        /// </summary>
        private decimal GetVolume(IBaseData data)
        {
            return data switch
            {
                TradeBar tradeBar => tradeBar.Volume,
                QuoteBar quoteBar => quoteBar.LastBidSize + quoteBar.LastAskSize,
                _ => 0m
            };
        }

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            LogManager.Log($"[DEBUG-WRAPPER-{_name}] Dispose() called");
            
            _wrappedConsolidator.DataConsolidated -= OnWrappedConsolidatorDataConsolidated;
            _wrappedConsolidator?.Dispose();
        }
    }
}
