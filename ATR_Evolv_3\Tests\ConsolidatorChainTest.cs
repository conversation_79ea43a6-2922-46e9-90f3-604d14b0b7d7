/*
 * Test to verify that DynamicRenkoConsolidator works correctly in SequentialConsolidator chains
 */

using System;
using System.Collections.Generic;
using QuantConnect.Data.Market;
using QuantConnect.Data.Consolidators;
using QuantConnect.Algorithm.CSharp;

namespace ATR_Evolv_3.Tests
{
    /// <summary>
    /// Simple test to verify that the DynamicRenkoConsolidator fix works correctly
    /// </summary>
    public class ConsolidatorChainTest
    {
        public static void RunTest()
        {
            Console.WriteLine("=== DynamicRenkoConsolidator Sequential Chain Test ===");
            
            // Create the consolidators
            var renkoConsolidator = new DynamicRenkoConsolidator(1.0m); // $1 Renko bars
            var dollarVolumeConsolidator = new DollarVolumeConsolidator(10000m); // $10,000 dollar volume bars
            
            // Create the sequential consolidator
            var sequentialConsolidator = new SequentialConsolidator(renkoConsolidator, dollarVolumeConsolidator);
            
            // Track events
            var renkoBarCount = 0;
            var dollarVolumeBarCount = 0;
            var finalOutputCount = 0;
            
            // Subscribe to events
            renkoConsolidator.DataConsolidated += (sender, bar) =>
            {
                renkoBarCount++;
                Console.WriteLine($"Renko Bar #{renkoBarCount}: {bar.Symbol} at {bar.Time}, Close={bar.Close:F2}, Volume={bar.Volume}");
            };
            
            dollarVolumeConsolidator.DataConsolidated += (sender, bar) =>
            {
                dollarVolumeBarCount++;
                Console.WriteLine($"Dollar Volume Bar #{dollarVolumeBarCount}: {bar.Symbol} at {bar.Time}, Close={bar.Close:F2}, Volume={bar.Volume}");
            };
            
            sequentialConsolidator.DataConsolidated += (sender, bar) =>
            {
                finalOutputCount++;
                Console.WriteLine($"Final Output #{finalOutputCount}: {bar.Symbol} at {bar.Time}, Close={bar.Close:F2}, Volume={bar.Volume}");
            };
            
            // Create test data
            var symbol = Symbol.Create("TEST", SecurityType.Equity, Market.USA);
            var baseTime = new DateTime(2024, 1, 1, 9, 30, 0);
            
            // Generate test trade bars with increasing prices to trigger Renko bars
            var testBars = new List<TradeBar>();
            for (int i = 0; i < 20; i++)
            {
                var price = 100.0m + (i * 0.5m); // Prices from $100.00 to $109.50
                var volume = 1000m + (i * 100m); // Volume from 1000 to 2900
                
                var bar = new TradeBar
                {
                    Symbol = symbol,
                    Time = baseTime.AddMinutes(i),
                    Open = price - 0.1m,
                    High = price + 0.1m,
                    Low = price - 0.2m,
                    Close = price,
                    Volume = volume
                };
                testBars.Add(bar);
            }
            
            // Feed data through the sequential consolidator
            Console.WriteLine("\n--- Feeding test data ---");
            foreach (var bar in testBars)
            {
                Console.WriteLine($"Input: {bar.Symbol} at {bar.Time}, Close={bar.Close:F2}, Volume={bar.Volume}");
                sequentialConsolidator.Update(bar);
            }
            
            // Report results
            Console.WriteLine("\n=== Test Results ===");
            Console.WriteLine($"Renko bars produced: {renkoBarCount}");
            Console.WriteLine($"Dollar volume bars produced: {dollarVolumeBarCount}");
            Console.WriteLine($"Final output bars: {finalOutputCount}");
            
            // Verify the fix worked
            if (renkoBarCount > 0 && dollarVolumeBarCount > 0 && finalOutputCount > 0)
            {
                Console.WriteLine("✅ SUCCESS: Sequential consolidator chain is working correctly!");
                Console.WriteLine("✅ DynamicRenkoConsolidator fix is working!");
            }
            else
            {
                Console.WriteLine("❌ FAILURE: Sequential consolidator chain is not working!");
                if (renkoBarCount == 0) Console.WriteLine("  - No Renko bars produced");
                if (dollarVolumeBarCount == 0) Console.WriteLine("  - No dollar volume bars produced (this was the original issue)");
                if (finalOutputCount == 0) Console.WriteLine("  - No final output produced");
            }
            
            Console.WriteLine("\n=== Test Complete ===");
        }
    }
}
