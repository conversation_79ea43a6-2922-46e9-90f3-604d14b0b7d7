#region imports
using System;
using System.Collections.Generic;
using System.Linq;
using QuantConnect;
using QuantConnect.Algorithm;
using QuantConnect.Algorithm.Framework;
using QuantConnect.Algorithm.Framework.Alphas;
using QuantConnect.Data;
using QuantConnect.Data.Consolidators;
using QuantConnect.Data.Market;
using QuantConnect.Indicators;
using QuantConnect.Scheduling; // Added for ScheduledEvent
using QuantConnect.Securities;
using QuantConnect.Data.UniverseSelection;
using QuantConnect.Interfaces; // Added for ITargetBarSizeChangeable
#endregion

namespace QuantConnect.Algorithm.CSharp
{
    /// <summary>
    /// Alpha model that combines DMI (Directional Movement Index) and ATR (Average True Range)
    /// to generate trade signals using a flip-flop approach for trend following.
    /// </summary>
    public class FlipFlopDmiAtrAlphaModel : AlphaModel
    {
        private readonly Dictionary<Symbol, SymbolData> _symbolData;
        private readonly Dictionary<Symbol, IDataConsolidator> _consolidators;
        private readonly Dictionary<Symbol, ScheduledEvent> _midnightResetEvents; // Added for managing reset events
        private readonly int _adxPeriod;
        private readonly int _atrPeriod;
        private readonly decimal _adxThreshold;
        private readonly decimal _multiplier;
        private readonly TimeSpan _consolidationPeriod; // Remains for the original time-based path

        // Renko fields
        private readonly bool _useChainedRenko;
        private readonly decimal _renkoVolumeBarSize;
        private readonly decimal _renkoPriceBarSize; // Will serve as fallback
        private readonly int _aggregationChunkSize; // New parameter for volume aggregation
        private readonly decimal _renkoPriceBarPercentage; // New parameter for price bar percentage
        private readonly RenkoPriceBarMode _renkoPriceBarMode;
        private readonly int _renkoAdaptiveRangeLevels;
        private readonly int _renkoAdaptiveRangeLookbackDays;
        private readonly decimal _renkoDollarBarValue; // New field

        // Store the specific underlying symbol we want to track
        private readonly Symbol _underlyingSymbol;

        // New components for better separation of concerns
        private readonly DmiAtrSignalGenerator _signalGenerator;
        private readonly ChartingService _chartingService;

        // The duration for which insights should remain active, in number of bars
        protected readonly int _insightBarCount;
        // The resolution associated with the insight bar count
        protected readonly Resolution _insightResolution;

        public FlipFlopDmiAtrAlphaModel(
            Symbol symbol,
            int adxPeriod,
            int atrPeriod,
            decimal adxThreshold,
            decimal multiplier,
            QCAlgorithm algorithm,
            TimeSpan consolidationPeriod, // Remains for the original consolidation path
            int insightBarCount,
            Resolution insightResolution,
            // Add Renko parameters to signature
            bool useChainedRenkoConsolidator = false,
            decimal renkoVolumeBarSize = 0m,
            decimal renkoPriceBarSize = 0m,
            int aggregationChunkSize = 15,
            decimal renkoPriceBarPercentage = 0.001m,
            RenkoPriceBarMode renkoPriceMode = RenkoPriceBarMode.Fixed,
            int renkoAdaptiveRangeLevels = 20, // New parameter
            int renkoAdaptiveRangeLookbackDays = 7, // New parameter for lookback
            decimal renkoDollarBarValue = 1000000m // New parameter
            )
        {
            _symbolData = [];
            _consolidators = new Dictionary<Symbol, IDataConsolidator>();
            _midnightResetEvents = new Dictionary<Symbol, ScheduledEvent>(); // Initialize dictionary
            _adxPeriod = adxPeriod;
            _atrPeriod = atrPeriod;
            _adxThreshold = adxThreshold;
            _multiplier = multiplier;
            _consolidationPeriod = consolidationPeriod; // Store for time based consolidation
            _underlyingSymbol = symbol; // Store the underlying symbol for filtering

            // Store new parameters
            _useChainedRenko = useChainedRenkoConsolidator;
            _renkoVolumeBarSize = renkoVolumeBarSize;
            _renkoPriceBarSize = renkoPriceBarSize;
            _aggregationChunkSize = aggregationChunkSize;
            _renkoPriceBarPercentage = renkoPriceBarPercentage;
            _renkoPriceBarMode = renkoPriceMode;
            _renkoAdaptiveRangeLevels = renkoAdaptiveRangeLevels; // Initialize new field
            _renkoAdaptiveRangeLookbackDays = renkoAdaptiveRangeLookbackDays; // Initialize new lookback field
            _renkoDollarBarValue = renkoDollarBarValue; // Initialize new field

            // Set insight duration and resolution
            _insightBarCount = insightBarCount;
            _insightResolution = insightResolution;

            // Initialize the helper services (LogManager parameter removed from constructors below)
            _signalGenerator = new DmiAtrSignalGenerator();
            _chartingService = new ChartingService(algorithm);

            LogManager.LogAlways($"[ALPHA-CONFIG] Initialized with insight duration: {_insightBarCount} bars at {_insightResolution} resolution. Renko Enabled: {_useChainedRenko}"); // Updated log message
        }

        public override void OnSecuritiesChanged(QCAlgorithm algorithm, SecurityChanges changes)
        {
            LogManager.Log($"[SECURITIES-CHANGED] OnSecuritiesChanged called with {changes.AddedSecurities.Count} added, {changes.RemovedSecurities.Count} removed");
            LogManager.Log($"[SECURITIES-CHANGED] Target underlying symbol: {_underlyingSymbol}");

            // Use the stored _algorithm instance consistently
            foreach (var security in changes.AddedSecurities)
            {
                LogManager.Log($"[SECURITIES-CHANGED] Processing added security: {security.Symbol} (Type: {security.Symbol.SecurityType})");

                // Filter out option contracts and only process the underlying symbol
                // Skip any security that's not our target underlying symbol
                if (security.Symbol != _underlyingSymbol)
                {
                    LogManager.Log($"[SECURITIES-CHANGED] Skipping {security.Symbol} - not our target underlying symbol");
                    continue;
                }

                LogManager.Log($"[ALPHA-FILTER] Adding indicators for {security.Symbol} (underlying security)");

                // Create new symbol data with all parameters, including insight resolution
                var symbolData = new SymbolData(security.Symbol, _adxPeriod, _atrPeriod, algorithm, _adxThreshold, _multiplier, _insightBarCount, _insightResolution);
                _symbolData[security.Symbol] = symbolData;

                if (_useChainedRenko)
                {
                    LogManager.Log($"[ALPHA_MODEL] Initializing Chained Renko for {security.Symbol}. Calculating initial first consolidator target value...");
                    decimal initialFirstConsolidatorTargetValue;
                    if (_renkoPriceBarMode == RenkoPriceBarMode.DollarVolumeInput)
                    {
                        initialFirstConsolidatorTargetValue = GetPreviousDayMedianDollarValue(algorithm, security.Symbol);
                        LogManager.Log($"[ALPHA_MODEL] Initial Renko params for {security.Symbol} (DollarInput): DollarValue={initialFirstConsolidatorTargetValue}");
                    }
                    else
                    {
                        initialFirstConsolidatorTargetValue = GetPreviousDayMedianVolume(algorithm, security.Symbol);
                        LogManager.Log($"[ALPHA_MODEL] Initial Renko params for {security.Symbol} (VolumeInput): VolumeSize={initialFirstConsolidatorTargetValue}");
                    }
                    decimal initialPriceSize = GetCurrentPriceBarSize(algorithm, security.Symbol); // This call remains the same
                    LogManager.Log($"[ALPHA_MODEL] Initial Renko params for {security.Symbol}: PriceSize={initialPriceSize} (determined by mode: {_renkoPriceBarMode})"); // Added logging for clarity
                    
                    SetupRenkoConsolidator(algorithm, security.Symbol, initialFirstConsolidatorTargetValue, initialPriceSize);

                    // Schedule the daily reset and recreation event
                    var scheduledEvent = algorithm.Schedule.On(
                        algorithm.DateRules.EveryDay(security.Symbol), // Use security.Symbol for exchange-aware date rules
                        algorithm.TimeRules.Midnight, // At midnight in the exchange's time zone
                        () => UpdateRenkoConsolidatorBarSizes(algorithm, security.Symbol) // Pass algorithm instance
                    );
                    _midnightResetEvents[security.Symbol] = scheduledEvent;
                    LogManager.Log($"[ALPHA_MODEL] Scheduled daily Renko consolidator reset and recreation for {security.Symbol} at midnight.");
                }
                else // Time-based consolidation
                {
                    LogManager.Log($"[ALPHA_MODEL] Setting up Time-Based Consolidator for {security.Symbol}: Period={_consolidationPeriod}");
                    var timeConsolidator = new TradeBarConsolidator(_consolidationPeriod);
                    // var timeConsolidator = new TickConsolidator(_consolidationPeriod);
                    
                    // Register the time-based consolidator
                    algorithm.SubscriptionManager.AddConsolidator(security.Symbol, timeConsolidator);
                    _consolidators[security.Symbol] = timeConsolidator; // Store the time-based consolidator

                    // Attach the event handler for the time-based consolidator
                    timeConsolidator.DataConsolidated += OnRenkoDataConsolidated; // Use the common handler
                    LogManager.Log($"[ALPHA_MODEL] Registered Time-Based Consolidator and attached OnRenkoDataConsolidated handler for {security.Symbol}.");
                }
            }

            foreach (var security in changes.RemovedSecurities)
            {
                // Only clean up the underlying symbol we're tracking
                if (security.Symbol != _underlyingSymbol)
                {
                    continue;
                }

                LogManager.Log($"[ALPHA-FILTER] Removing indicators for {security.Symbol} (underlying security)");

                _symbolData.Remove(security.Symbol);

                // Use TryGetValue for safer removal and avoid double lookup
                if (_consolidators.TryGetValue(security.Symbol, out var consolidatorToRemove))
                {
                    // Unsubscribe the event handler
                    // consolidatorToRemove is IDataConsolidator, so we can directly access DataConsolidated
                    consolidatorToRemove.DataConsolidated -= OnRenkoDataConsolidated;
                    LogManager.Log($"[ALPHA_MODEL] Unsubscribed OnRenkoDataConsolidated for {security.Symbol}.");

                    // Properly unregister the consolidator from the algorithm
                    algorithm.SubscriptionManager.RemoveConsolidator(security.Symbol, consolidatorToRemove);
                    _consolidators.Remove(security.Symbol); // Remove from our tracking
                    LogManager.Log($"[ALPHA_MODEL] Removed and unregistered consolidator for {security.Symbol}.");

                    // If a midnight reset event was scheduled for this Renko consolidator, remove it
                    if (_midnightResetEvents.TryGetValue(security.Symbol, out var eventToRemove))
                    {
                        algorithm.Schedule.Remove(eventToRemove);
                        _midnightResetEvents.Remove(security.Symbol);
                        LogManager.Log($"[ALPHA_MODEL] Unscheduled midnight reset for {security.Symbol}.");
                    }
                }
            }
        }
        private void SetupRenkoConsolidator(QCAlgorithm algorithm, Symbol symbol, decimal firstConsolidatorTargetValue, decimal priceBarSize)
        {
            IDataConsolidator firstConsolidator;
            if (_renkoPriceBarMode == RenkoPriceBarMode.DollarVolumeInput)
            {
                LogManager.Log($"[ALPHA_MODEL] SetupRenkoConsolidator for {symbol}: Using RenkoCompatibleDollarVolumeConsolidator with CONSTANT target=$1000 (FIXED: Will always trigger consolidation)");
                firstConsolidator = new RenkoCompatibleDollarVolumeConsolidator(firstConsolidatorTargetValue); // Parameters ignored - uses constant $1000 target
                // firstConsolidator = new DollarVolumeConsolidator(firstConsolidatorTargetValue);
                // firstConsolidator = new DollarTradeBarConsolidator(firstConsolidatorTargetValue);
            }
            else // Existing volume-based logic
            {
                LogManager.Log($"[ALPHA_MODEL] SetupRenkoConsolidator for {symbol}: Using VolumeRenkoConsolidator with TargetValue={firstConsolidatorTargetValue}");
                firstConsolidator = new VolumeConsolidator(firstConsolidatorTargetValue);
                // firstConsolidator = new VolumeTradeBarConsolidator(firstConsolidatorTargetValue);
                // firstConsolidator = new VolumeRenkoConsolidator(firstConsolidatorTargetValue);
            }


            // var priceConsolidator = new ClassicRenkoConsolidator(priceBarSize);
            // var priceConsolidator = new DynamicClassicRenkoConsolidator(priceBarSize);
            // var priceConsolidator = new RenkoConsolidator(priceBarSize);
            // var priceConsolidator = new WickedRenkoConsolidator(priceBarSize);
            var priceConsolidator = new DynamicRenkoConsolidator(priceBarSize);
            // var priceConsolidator = new TimeDistributedRenkoConsolidator(priceBarSize, TimeSpan.FromSeconds(1)) ;
            // priceConsolidator.Type = RenkoType.Classic; // Set the type to Classic
            // TESTING SPECIFIC ORDER: DynamicRenkoConsolidator -> DollarVolumeConsolidator
            // This is the order you want to test, even though it requires RenkoBar -> TradeBar compatibility
            // Using DebugSequentialConsolidator for enhanced logging
            LogManager.Log($"[CONSOLIDATOR-SETUP] Creating DebugSequentialConsolidator with order: {priceConsolidator.GetType().Name} -> {firstConsolidator.GetType().Name}");

            // Wrap the second consolidator with debug wrapper to track all data it receives
            var wrappedSecondConsolidator = new DebugConsolidatorWrapper(firstConsolidator, "SECOND");

            var sequentialConsolidator = new DebugSequentialConsolidator(priceConsolidator, wrappedSecondConsolidator);
            LogManager.Log($"[CONSOLIDATOR-SETUP] DebugSequentialConsolidator created successfully. InputType: {sequentialConsolidator.InputType.Name}, OutputType: {sequentialConsolidator.OutputType.Name}");
            // var sequentialConsolidator = new SequentialConsolidator(firstConsolidator, priceConsolidator); // OPPOSITE ORDER
            // var sequentialConsolidator = firstConsolidator;
            // var sequentialConsolidator = priceConsolidator;

            algorithm.SubscriptionManager.AddConsolidator(symbol, sequentialConsolidator);
            sequentialConsolidator.DataConsolidated += OnRenkoDataConsolidated;
            _consolidators[symbol] = sequentialConsolidator;

            // Enhanced logging to verify the consolidator chain setup (YOUR SPECIFIC ORDER)
            LogManager.Log($"[CONSOLIDATOR-SETUP] SUCCESS: DebugSequentialConsolidator chain created for {symbol}:");
            LogManager.Log($"[CONSOLIDATOR-SETUP]   First (receives raw data): {priceConsolidator.GetType().Name}({priceBarSize})");
            LogManager.Log($"[CONSOLIDATOR-SETUP]   Second (wrapped, receives Renko output): DebugConsolidatorWrapper -> {firstConsolidator.GetType().Name}({firstConsolidatorTargetValue})");
            LogManager.Log($"[CONSOLIDATOR-SETUP]   Data flow: Raw Data -> {priceConsolidator.GetType().Name} -> DebugWrapper({firstConsolidator.GetType().Name}) -> OnRenkoDataConsolidated");
            LogManager.Log($"[CONSOLIDATOR-SETUP] WATCH: Look for '[DEBUG-SEQUENTIAL-*]' and '[CONSOLIDATOR-FIX-SUCCESS]' messages to verify the fix is working!");
        }

        private void OnRenkoDataConsolidated(object sender, IBaseData consolidatedBar)
        {
            // Enhanced logging to verify consolidator chain fix
            string senderType = sender?.GetType().Name ?? "Unknown";
            LogManager.Log($"[CONSOLIDATOR-FIX-VERIFY] OnRenkoDataConsolidated called: Sender={senderType}, Symbol={consolidatedBar?.Symbol}, Time={consolidatedBar?.Time}, EndTime={consolidatedBar?.EndTime}");

            // Key verification: If sender is DebugSequentialConsolidator, the fix is working!
            if (senderType == "DebugSequentialConsolidator")
            {
                LogManager.Log($"[CONSOLIDATOR-FIX-SUCCESS] SUCCESS: RenkoCompatible consolidator chain fix is working! Data flowing through DebugSequentialConsolidator for {consolidatedBar?.Symbol}");

                // Additional diagnostic: Check what type of data we're receiving
                if (consolidatedBar is VolumeBar volumeBar)
                {
                    LogManager.Log($"[CONSOLIDATOR-OUTPUT] Received VolumeBar from RenkoCompatibleDollarVolumeConsolidator: Volume={volumeBar.Volume:F0}, Close={volumeBar.Close:F2}");
                }
                else if (consolidatedBar is RenkoBar renkoBar)
                {
                    LogManager.Log($"[CONSOLIDATOR-OUTPUT] Received RenkoBar: Volume={renkoBar.Volume:F0}, Close={renkoBar.Close:F2}");
                }
                else
                {
                    LogManager.Log($"[CONSOLIDATOR-OUTPUT] Received unexpected data type: {consolidatedBar?.GetType().Name}");
                }
            }

            if (!_symbolData.TryGetValue(consolidatedBar.Symbol, out var symbolData))
            {
                LogManager.Log($"[ALPHA_MODEL] OnRenkoDataConsolidated: SymbolData not found for {consolidatedBar.Symbol}. Consolidator: {senderType}");
                return;
            }

            if (consolidatedBar is TradeBar bar) // RenkoBar inherits from TradeBar
            {
                // Log bar type to verify what's coming through the chain
                string barType = bar.GetType().Name;
                LogManager.Log($"[CONSOLIDATOR-CHAIN] Bar type: {barType}, OHLCV=({bar.Open:F2}, {bar.High:F2}, {bar.Low:F2}, {bar.Close:F2}, {bar.Volume:F0}), Period={bar.Period}");

                symbolData.UpdateWithConsolidated(bar);

                if (symbolData.IsReady)
                {
                    _signalGenerator.ProcessSignals(symbolData);
                    _chartingService.PlotPriceAndIndicators(symbolData);
                    LogManager.LogThrottled($"[ALPHA_MODEL] SUCCESS: Successfully processed {barType} for {consolidatedBar.Symbol} at {consolidatedBar.Time}. SymbolData is Ready.", TimeSpan.FromMinutes(5));
                }
                else
                {
                    LogManager.LogThrottled($"[ALPHA_MODEL] Updated SymbolData for {consolidatedBar.Symbol} with {barType}, but not yet ready (warmup phase).", TimeSpan.FromMinutes(10));
                }
            }
            else
            {
                LogManager.Log($"[ALPHA_MODEL] ERROR: Consolidator for {consolidatedBar.Symbol} emitted unexpected data type: {consolidatedBar?.GetType().Name}");
            }
        }

        private void UpdateRenkoConsolidatorBarSizes(QCAlgorithm algorithm, Symbol symbol)
        {
            return; // Temporarily disable this method to avoid issues with the new consolidator chain
            
            LogManager.Log($"[ALPHA_MODEL] UpdateRenkoConsolidatorBarSizes for {symbol} at {algorithm.Time}");

            // Calculate all potential bar sizes upfront
            decimal volumeBarSize = GetPreviousDayMedianVolume(algorithm, symbol);
            decimal dollarVolumeBarSize = GetPreviousDayMedianDollarValue(algorithm, symbol);
            decimal priceBarSize = GetCurrentPriceBarSize(algorithm, symbol);

            LogManager.Log($"[ALPHA_MODEL] Calculated bar sizes for {symbol}: VolumeBarSize={volumeBarSize}, DollarVolumeBarSize={dollarVolumeBarSize}, PriceBarSize={priceBarSize}");

            // Get the existing consolidator - it should exist
            if (!_consolidators.TryGetValue(symbol, out var consolidator))
            {
                LogManager.Log($"[CONSOLIDATOR-MISSING] ERROR: No consolidator found for {symbol}! This means OnSecuritiesChanged was never called or failed.");
                LogManager.Log($"[CONSOLIDATOR-MISSING] Available consolidators: {string.Join(", ", _consolidators.Keys)}");
                LogManager.Log($"[CONSOLIDATOR-MISSING] This explains why no data is flowing - the consolidators don't exist!");
                throw new InvalidOperationException($"Cannot update consolidator bar sizes for {symbol} - no consolidator found.");
            }

            // Update the consolidator recursively, handling any level of nesting
            bool success = UpdateConsolidatorRecursively(consolidator, volumeBarSize, dollarVolumeBarSize, priceBarSize, symbol);

            if (!success)
            {
                throw new InvalidOperationException($"Failed to update one or more consolidators for {symbol}.");
            }

            LogManager.Log($"[ALPHA_MODEL] Successfully updated all consolidators for {symbol}");
        }

        private bool UpdateConsolidatorRecursively(IDataConsolidator consolidator, decimal volumeBarSize, decimal dollarVolumeBarSize, decimal priceBarSize, Symbol symbol)
        {
            // Log detailed info about the consolidator being processed
            LogManager.Log($"[DEBUG-UPDATE] Processing consolidator of type: {consolidator.GetType().Name} for {symbol}");

            // Handle SequentialConsolidator by recursively processing its internal consolidators
            if (consolidator is SequentialConsolidator sequentialConsolidator)
            {
                LogManager.Log($"[DEBUG-UPDATE] Found SequentialConsolidator for {symbol}");

                var firstConsolidator = sequentialConsolidator.First;
                var secondConsolidator = sequentialConsolidator.Second;

                // Log details about the internal consolidators
                LogManager.Log($"[DEBUG-UPDATE] SequentialConsolidator for {symbol} has First: {firstConsolidator?.GetType().Name}, Second: {secondConsolidator?.GetType().Name}");

                // Ensure both exist
                if (firstConsolidator == null || secondConsolidator == null)
                {
                    LogManager.Log($"[ALPHA_MODEL] Sequential consolidator for {symbol} has null First or Second consolidator");
                    return false;
                }

                // Recursively update both consolidators
                bool firstSuccess = UpdateConsolidatorRecursively(firstConsolidator, volumeBarSize, dollarVolumeBarSize, priceBarSize, symbol);
                bool secondSuccess = UpdateConsolidatorRecursively(secondConsolidator, volumeBarSize, dollarVolumeBarSize, priceBarSize, symbol);

                return firstSuccess && secondSuccess;
            }

            // Handle DebugSequentialConsolidator by recursively processing its internal consolidators
            if (consolidator is DebugSequentialConsolidator debugSequentialConsolidator)
            {
                LogManager.Log($"[DEBUG-UPDATE] Found DebugSequentialConsolidator for {symbol}");

                // Access the internal consolidators through public properties
                var firstConsolidator = debugSequentialConsolidator.First;
                var secondConsolidator = debugSequentialConsolidator.Second;

                // Log details about the internal consolidators
                LogManager.Log($"[DEBUG-UPDATE] DebugSequentialConsolidator for {symbol} has First: {firstConsolidator?.GetType().Name}, Second: {secondConsolidator?.GetType().Name}");

                // Handle DebugConsolidatorWrapper in the second position
                if (secondConsolidator is DebugConsolidatorWrapper wrapper)
                {
                    LogManager.Log($"[DEBUG-UPDATE] Second consolidator is DebugConsolidatorWrapper, accessing wrapped consolidator");
                    // We need to access the wrapped consolidator, but it's private
                    // For now, skip updating the wrapper since RenkoCompatibleDollarVolumeConsolidator uses constant target anyway
                    LogManager.Log($"[DEBUG-UPDATE] Skipping update of DebugConsolidatorWrapper (wrapped consolidator uses constant target)");
                    secondConsolidator = null; // Skip this one
                }

                // Ensure both exist
                if (firstConsolidator == null || secondConsolidator == null)
                {
                    LogManager.Log($"[ALPHA_MODEL] Sequential consolidator for {symbol} has null First or Second consolidator");
                    return false;
                }

                // Recursively update both consolidators
                bool firstSuccess = UpdateConsolidatorRecursively(firstConsolidator, volumeBarSize, dollarVolumeBarSize, priceBarSize, symbol);
                bool secondSuccess = UpdateConsolidatorRecursively(secondConsolidator, volumeBarSize, dollarVolumeBarSize, priceBarSize, symbol);

                return firstSuccess && secondSuccess;
            }

            // Handle direct ITargetBarSizeChangeable implementation
            if (consolidator is ITargetBarSizeChangeable targetBarSizeChangeable)
            {
                // Special handling for RenkoCompatibleDollarVolumeConsolidator - skip updates since it uses constant target
                if (consolidator is RenkoCompatibleDollarVolumeConsolidator)
                {
                    LogManager.Log($"[DEBUG-UPDATE-DETAIL] RenkoCompatibleDollarVolumeConsolidator for {symbol} uses constant target ($1000). Skipping daily update.");
                    return true; // Skip update since it uses a constant target
                }

                // Get current bar size before update for comparison
                decimal currentBarSize = targetBarSizeChangeable.TargetBarSize;

                // Determine the appropriate bar size based on consolidator type
                decimal appropriateBarSize;
                string consolidatorTypeName = consolidator.GetType().Name;
                LogManager.Log($"[DEBUG-UPDATE-DETAIL] Evaluating consolidator: {consolidatorTypeName} for {symbol}. Available sizes: Volume={volumeBarSize}, DollarVolume={dollarVolumeBarSize}, Price={priceBarSize}");

                if (consolidator is DollarVolumeConsolidator) // Check for the most specific type first
                {
                    appropriateBarSize = dollarVolumeBarSize;
                    LogManager.Log($"[DEBUG-UPDATE-DETAIL] Branch: DollarVolumeConsolidator (actual type: {consolidatorTypeName}). Selected dollarVolumeBarSize: {appropriateBarSize}. Current: {currentBarSize}, Change: {appropriateBarSize - currentBarSize}");
                }
                else if (consolidator is VolumeConsolidator) // Then check for VolumeConsolidator
                {
                    appropriateBarSize = volumeBarSize;
                    LogManager.Log($"[DEBUG-UPDATE-DETAIL] Branch: VolumeConsolidator (actual type: {consolidatorTypeName}). Selected volumeBarSize: {appropriateBarSize}. Current: {currentBarSize}, Change: {appropriateBarSize - currentBarSize}");
                }
                else if (consolidator is DynamicRenkoConsolidator || consolidator is DynamicClassicRenkoConsolidator || consolidator is TimeDistributedRenkoConsolidator)
                {
                    appropriateBarSize = priceBarSize;
                    LogManager.Log($"[DEBUG-UPDATE-DETAIL] Branch: Renko type (actual type: {consolidatorTypeName}). Selected priceBarSize: {appropriateBarSize}. Current: {currentBarSize}, Change: {appropriateBarSize - currentBarSize}");
                }
                else
                {
                    LogManager.Log($"[DEBUG-UPDATE-DETAIL] Unknown consolidator type {consolidatorTypeName} for {symbol} implements ITargetBarSizeChangeable but its appropriate bar size is unknown");
                    return false;
                }
                LogManager.Log($"[DEBUG-UPDATE-DETAIL] For {consolidatorTypeName} on {symbol}, chosen appropriateBarSize = {appropriateBarSize}");

                // Check if there's actually a change
                if (currentBarSize == appropriateBarSize)
                {
                    LogManager.Log($"[DEBUG-UPDATE] No change needed for {consolidator.GetType().Name} for {symbol} - bar size remains {currentBarSize}");
                    return true; // No change needed
                }

                // Update the bar size
                LogManager.Log($"[DEBUG-UPDATE] UPDATING {consolidator.GetType().Name} TargetBarSize from {currentBarSize} to {appropriateBarSize} for {symbol}");
                targetBarSizeChangeable.TargetBarSize = appropriateBarSize;

                // Log completion of update
                LogManager.Log($"[DEBUG-UPDATE] Successfully updated {consolidator.GetType().Name} for {symbol}");
                return true;
            }

            // If we get here, the consolidator doesn't implement ITargetBarSizeChangeable
            LogManager.Log($"[DEBUG-UPDATE] Consolidator of type {consolidator.GetType().Name} for {symbol} does not implement ITargetBarSizeChangeable");
            return false;
        }

        private decimal GetCurrentPriceBarSize(QCAlgorithm algorithm, Symbol symbol)
        {
            LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize for {symbol} at {algorithm.Time}, Mode: {_renkoPriceBarMode}");

            if (_renkoPriceBarMode == RenkoPriceBarMode.Fixed)
            {
                LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Mode is Fixed. Using _renkoPriceBarSize: {_renkoPriceBarSize}");
                return _renkoPriceBarSize;
            }
            else if (_renkoPriceBarMode == RenkoPriceBarMode.Percentage)
            {
                LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Mode is Percentage.");
                if (_renkoPriceBarPercentage <= 0)
                {
                    LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Percentage mode selected, but _renkoPriceBarPercentage is not positive ({_renkoPriceBarPercentage}). Falling back to fixed _renkoPriceBarSize: {_renkoPriceBarSize}");
                    return _renkoPriceBarSize;
                }

                decimal underlyingPrice = 0m;
                var security = algorithm.Securities[symbol];

                if (security != null && security.Price > 0)
                {
                    underlyingPrice = security.Price;
                    LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Using current security price for {symbol}: {underlyingPrice}");
                }
                else
                {
                    LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Current price for {symbol} is 0 or security is null. Attempting to use previous day's close.");
                    try
                    {
                        // Request only 1 bar for the previous day's close.
                        var history = algorithm.History<TradeBar>(symbol, TimeSpan.FromDays(_renkoAdaptiveRangeLookbackDays), Resolution.Daily, extendedMarketHours: false);
                        if (history != null && history.Any())
                        {
                            var previousDayBar = history.LastOrDefault(); // Should be the most recent daily bar
                            if (previousDayBar != null && previousDayBar.Close > 0)
                            {
                                underlyingPrice = previousDayBar.Close;
                                LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Using previous day's close for {symbol}: {underlyingPrice} from {previousDayBar.Time.ToShortDateString()}");
                            }
                            else
                            {
                                LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Previous day's close for {symbol} is 0 or bar is null.");
                            }
                        }
                        else
                        {
                            LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: No daily history found for {symbol} to get previous day's close.");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Error fetching daily history for {symbol}: {ex.Message}.");
                    }
                }

                if (underlyingPrice > 0)
                {
                    decimal dynamicPriceBarSize = underlyingPrice * _renkoPriceBarPercentage;
                    if (dynamicPriceBarSize > 0)
                    {
                        LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Calculated dynamic price bar size for {symbol}: {dynamicPriceBarSize} (Price: {underlyingPrice}, Pct: {_renkoPriceBarPercentage})");
                        return dynamicPriceBarSize;
                    }
                    else
                    {
                        LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Calculated dynamic price bar size for {symbol} is not positive ({dynamicPriceBarSize}). Falling back to fixed _renkoPriceBarSize: {_renkoPriceBarSize}");
                        return _renkoPriceBarSize;
                    }
                }
                else
                {
                    LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Underlying price for {symbol} is still 0 after checks. Falling back to fixed _renkoPriceBarSize: {_renkoPriceBarSize}");
                    return _renkoPriceBarSize;
                }
            }
            else //if (_renkoPriceBarMode == RenkoPriceBarMode.AdaptiveRange)
            {
                LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Mode is AdaptiveRange. Levels: {_renkoAdaptiveRangeLevels}, LookbackDays: {_renkoAdaptiveRangeLookbackDays}");
                if (_renkoAdaptiveRangeLevels <= 0)
                {
                    LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: AdaptiveRange mode, but _renkoAdaptiveRangeLevels is not positive ({_renkoAdaptiveRangeLevels}). Falling back to fixed _renkoPriceBarSize: {_renkoPriceBarSize}");
                    return _renkoPriceBarSize;
                }
                if (_renkoAdaptiveRangeLookbackDays <= 0)
                {
                    LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: AdaptiveRange mode, but _renkoAdaptiveRangeLookbackDays is not positive ({_renkoAdaptiveRangeLookbackDays}). Falling back to fixed _renkoPriceBarSize: {_renkoPriceBarSize}");
                    return _renkoPriceBarSize;
                }

                List<TradeBar> minuteBars;
                try
                {
                    // Fetch minute TradeBar data for the configured number of calendar days preceding the current algorithm time.
                    // History call for a TimeSpan ends at algorithm.Time
                    var historyData = algorithm.History<TradeBar>(symbol, TimeSpan.FromDays(_renkoAdaptiveRangeLookbackDays), Resolution.Minute, extendedMarketHours: false);
                    minuteBars = historyData.ToList();
                    LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Fetched {minuteBars.Count} minute bars for {symbol} over the last {_renkoAdaptiveRangeLookbackDays} calendar days for AdaptiveRange (ending {algorithm.Time}).");
                }
                catch (Exception ex)
                {
                    LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Error fetching minute history for {symbol} for AdaptiveRange (lookback {_renkoAdaptiveRangeLookbackDays} days): {ex.Message}. Falling back to fixed _renkoPriceBarSize: {_renkoPriceBarSize}");
                    return _renkoPriceBarSize;
                }

                if (!minuteBars.Any())
                {
                    LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: No minute data found for {symbol} in the last {_renkoAdaptiveRangeLookbackDays} days for AdaptiveRange. Falling back to fixed _renkoPriceBarSize: {_renkoPriceBarSize}");
                    return _renkoPriceBarSize;
                }

                var dailyRanges = minuteBars
                    .GroupBy(bar => bar.Time.Date) // Group by date
                    .Select(dayGroup =>
                    {
                        if (!dayGroup.Any()) return 0m; // Should not happen if group exists
                        var minClose = dayGroup.Min(bar => bar.Close);
                        var maxClose = dayGroup.Max(bar => bar.Close);
                        var range = maxClose - minClose;
                        // LogManager.Log($"[ALPHA_MODEL] AdaptiveRange: Day {dayGroup.Key.ToShortDateString()}, MinC={minClose}, MaxC={maxClose}, Range={range}");
                        return range;
                    })
                    .Where(range => range > 0m) // Collect only ranges greater than zero
                    .ToList();

                if (!dailyRanges.Any())
                {
                    LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: No valid daily ranges (>0) found for {symbol} in the last {_renkoAdaptiveRangeLookbackDays} days from {minuteBars.Count} bars. Falling back to fixed _renkoPriceBarSize: {_renkoPriceBarSize}");
                    return _renkoPriceBarSize;
                }

                decimal averageDailyDelta = dailyRanges.Average();
                LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Calculated average daily delta for {symbol}: {averageDailyDelta:F5} from {dailyRanges.Count} valid daily ranges.");

                if (averageDailyDelta > 0m) // _renkoAdaptiveRangeLevels already checked to be > 0
                {
                    decimal calculatedBarSize = averageDailyDelta / _renkoAdaptiveRangeLevels;
                    decimal finalBarSize = Math.Max(0.01m, calculatedBarSize);

                    if (finalBarSize != calculatedBarSize && calculatedBarSize < 0.01m) // Log only if adjusted up
                    {
                        LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Calculated bar size {calculatedBarSize:F5} for {symbol} was less than 0.01m. Adjusted to {finalBarSize:F2}.");
                    }
                    LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: AdaptiveRange calculated final bar size for {symbol}: {finalBarSize:F5}");
                    return finalBarSize;
                }
                else
                {
                    LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Average daily delta ({averageDailyDelta:F5}) is not positive for {symbol}. Falling back to fixed _renkoPriceBarSize: {_renkoPriceBarSize}");
                    return _renkoPriceBarSize;
                }
            }
            // else
            // {
            //     // Should not happen if RenkoPriceBarMode enum is used correctly
            //     LogManager.Log($"[ALPHA_MODEL] GetCurrentPriceBarSize: Unknown RenkoPriceBarMode: {(int)_renkoPriceBarMode} for symbol {symbol}. Falling back to fixed _renkoPriceBarSize: {_renkoPriceBarSize}");
            //     return _renkoPriceBarSize;
            // }
        }

        /// <summary>
        /// Recursively checks for and processes any TimeDistributedRenkoConsolidator instances 
        /// at any level of nesting within a consolidator hierarchy
        /// </summary>
        /// <param name="consolidator">The consolidator to check</param>
        /// <param name="currentTime">The current algorithm time</param>
        private void ProcessTimeDistributedRenkoConsolidators(IDataConsolidator consolidator, DateTime currentTime)
        {
            // Check if it's a TimeDistributedRenkoConsolidator
            if (consolidator is TimeDistributedRenkoConsolidator timeDistributedRenko)
            {
                timeDistributedRenko.ProcessPendingBarsNow(currentTime);
            }
            
            // Check if it's a SequentialConsolidator and recursively process its components
            if (consolidator is SequentialConsolidator sequential)
            {
                // Check both first and second consolidators
                if (sequential.First != null)
                {
                    ProcessTimeDistributedRenkoConsolidators(sequential.First, currentTime);
                }
                
                if (sequential.Second != null)
                {
                    ProcessTimeDistributedRenkoConsolidators(sequential.Second, currentTime);
                }
            }
        }
        
    private decimal GetPreviousDayMedianDollarValue(QCAlgorithm algorithm, Symbol symbol)
    {
        LogManager.Log($"[ALPHA_MODEL] GetPreviousDayMedianDollarValue for {symbol} at {algorithm.Time}");
        try
        {
            var history = algorithm.History<TradeBar>(symbol, TimeSpan.FromDays(_renkoAdaptiveRangeLookbackDays), Resolution.Minute, extendedMarketHours: false);

            if (history == null || !history.Any())
            {
                LogManager.Log($"[ALPHA_MODEL] GetPreviousDayMedianDollarValue: No history data for {symbol}. Using fallback: {_renkoDollarBarValue}");
                return _renkoDollarBarValue;
            }

            var chunkedDollarValues = new List<decimal>();
            decimal currentChunkDollarValue = 0m;
            int barsInCurrentChunk = 0;

            if (_aggregationChunkSize <= 0)
            {
                LogManager.Log($"[ALPHA_MODEL] GetPreviousDayMedianDollarValue: Invalid _aggregationChunkSize: {_aggregationChunkSize}. Using fallback: {_renkoDollarBarValue}");
                return _renkoDollarBarValue;
            }

            foreach (var bar in history)
            {
                currentChunkDollarValue += bar.Close * bar.Volume; // Dollar value for this bar
                // currentChunkDollarValue += (bar.Open + bar.Close)/2 * bar.Volume; // Dollar value for this bar
                barsInCurrentChunk++;

                if (barsInCurrentChunk == _aggregationChunkSize)
                {
                    chunkedDollarValues.Add(currentChunkDollarValue);
                    currentChunkDollarValue = 0m;
                    barsInCurrentChunk = 0;
                }
            }
            // Partial chunks are discarded as per current volume logic.

            if (!chunkedDollarValues.Any())
            {
                LogManager.Log($"[ALPHA_MODEL] GetPreviousDayMedianDollarValue: Not enough data for full chunks for {symbol}. Using fallback: {_renkoDollarBarValue}");
                return _renkoDollarBarValue;
            }

            chunkedDollarValues.Sort();
            decimal medianDollarValue;
            int count = chunkedDollarValues.Count;
            if (count % 2 == 0)
            {
                medianDollarValue = (chunkedDollarValues[count / 2 - 1] + chunkedDollarValues[count / 2]) / 2;
            }
            else
            {
                medianDollarValue = chunkedDollarValues[count / 2];
            }

            LogManager.Log($"[ALPHA_MODEL] GetPreviousDayMedianDollarValue for {symbol}: Median dollar value: {medianDollarValue} from {count} chunks.");
            return medianDollarValue > 0 ? medianDollarValue : _renkoDollarBarValue;
        }
        catch (Exception ex)
        {
            LogManager.Log($"[ALPHA_MODEL] GetPreviousDayMedianDollarValue: Error for {symbol}: {ex.Message}. Using fallback: {_renkoDollarBarValue}");
            return _renkoDollarBarValue;
        }
    }

        private decimal GetPreviousDayMedianVolume(QCAlgorithm algorithm, Symbol symbol)
        {
            LogManager.Log($"[ALPHA_MODEL] GetPreviousDayMedianVolume for {symbol} at {algorithm.Time}");
            try
            {
                // Request 1 day of minute resolution TradeBar data for the previous trading day.
                // The history request is for the 24 hours leading up to the current algorithm time (midnight).
                var history = algorithm.History<TradeBar>(symbol, TimeSpan.FromDays(_renkoAdaptiveRangeLookbackDays), Resolution.Minute, extendedMarketHours: false);

                if (history == null || !history.Any())
                {
                    LogManager.Log($"[ALPHA_MODEL] GetPreviousDayMedianVolume: No history data found for {symbol} for the previous day. Using default volume size: {_renkoVolumeBarSize}");
                    return _renkoVolumeBarSize;
                }

                // Use the configurable chunk size
                var fifteenMinuteVolumes = new List<decimal>();
                decimal currentChunkVolume = 0m;
                int barsInCurrentChunk = 0;

                // Ensure _volumeAggregationChunkSize is positive to prevent infinite loops or division by zero.
                if (_aggregationChunkSize <= 0)
                {
                    LogManager.Log($"[ALPHA_MODEL] GetPreviousDayMedianVolume: Invalid _volumeAggregationChunkSize: {_aggregationChunkSize}. Using default volume size: {_renkoVolumeBarSize}");
                    return _renkoVolumeBarSize;
                }

                foreach (var bar in history)
                {
                    currentChunkVolume += bar.Volume;
                    barsInCurrentChunk++;

                    if (barsInCurrentChunk == _aggregationChunkSize)
                    {
                        fifteenMinuteVolumes.Add(currentChunkVolume);
                        currentChunkVolume = 0m;
                        barsInCurrentChunk = 0;
                    }
                }
                // The plan specifies to discard the final partial chunk, so no need to add it if barsInCurrentChunk < chunkSize.

                if (!fifteenMinuteVolumes.Any())
                {
                    LogManager.Log($"[ALPHA_MODEL] GetPreviousDayMedianVolume: Not enough data to form any 15-minute volume chunks for {symbol}. Using default volume size: {_renkoVolumeBarSize}");
                    return _renkoVolumeBarSize;
                }

                fifteenMinuteVolumes.Sort();
                decimal medianVolume;
                int count = fifteenMinuteVolumes.Count;
                if (count % 2 == 0)
                {
                    // Even number of elements: average of the two middle elements
                    medianVolume = (fifteenMinuteVolumes[count / 2 - 1] + fifteenMinuteVolumes[count / 2]) / 2;
                }
                else
                {
                    // Odd number of elements: the middle element
                    medianVolume = fifteenMinuteVolumes[count / 2];
                }

                LogManager.Log($"[ALPHA_MODEL] GetPreviousDayMedianVolume for {symbol}: Calculated median 15-min volume: {medianVolume} from {count} chunks. History bars: {history.Count()}");
                return medianVolume > 0 ? medianVolume : _renkoVolumeBarSize; // Ensure median is positive
            }
            catch (Exception ex)
            {
                LogManager.Log($"[ALPHA_MODEL] GetPreviousDayMedianVolume: Error calculating median volume for {symbol}. Error: {ex.Message}. Using default volume size: {_renkoVolumeBarSize}");
                return _renkoVolumeBarSize;
            }
        }

        public override IEnumerable<Insight> Update(QCAlgorithm algorithm, Slice data)
        {
            algorithm.Insights.RemoveExpiredInsights(algorithm.Time); // Remove expired insights
            
            // Process any pending bars in TimeDistributedRenkoConsolidators
            foreach (var consolidator in _consolidators.Values)
            {
                // Check for TimeDistributedRenkoConsolidators at any level of nesting
                ProcessTimeDistributedRenkoConsolidators(consolidator, data.Time);
            }
            
            // Check if we're 10 minutes from market close for any security
            // If so, cancel all insights and return empty list
            // foreach (var kvp in _symbolData)
            // {
            //     var symbol = kvp.Key;
            //     var security = algorithm.Securities[symbol];
                
            //     // IsClosingSoon checks if the exchange will be closed within the specified number of minutes
            //     if (security.Exchange.IsClosingSoon(10))
            //     {
            //         LogManager.Log($"[MARKET-CLOSING-SOON] Market for {symbol} is closing within 10 minutes. Cancelling all insights.");
                    
            //         // Get all active insights at the current time
            //         var allActiveInsights = algorithm.Insights.GetActiveInsights(data.Time);
                    
            //         // Cancel all active insights
            //         foreach (var insight in allActiveInsights)
            //         {
            //             LogManager.Log($"[MARKET-CLOSING-SOON] Cancelling insight Id: {insight.Id}, Symbol: {insight.Symbol}, Direction: {insight.Direction}");
            //             insight.Cancel(data.Time);
            //         }
                    
            //         // Return empty list as requested
            //         return new List<Insight>();
            //     }
            // }
            
            // Create a new list to hold insights that will be emitted
            var insights = new List<Insight>();

            // Generate new insights for each symbol based on current signal direction
            foreach (var kvp in _symbolData)
            {
                var symbol = kvp.Key;
                var sd = kvp.Value;

                var underlyingSecurity = algorithm.Securities[symbol];
                if (!underlyingSecurity.Exchange.ExchangeOpen)
                {
                    LogManager.LogThrottled($"[OPT-UPDATE] Market closed for underlying {symbol}. Skipping update.", TimeSpan.FromMinutes(60));
                    continue; // Skip if market is closed
                }

                // Skip if indicators aren't ready
                if (!sd.IsReady) continue;

                // Convert signal direction (-1, 0, 1) to InsightDirection
                InsightDirection direction = sd.SignalDirection switch
                {
                    -1 => InsightDirection.Down,
                    1 => InsightDirection.Up,
                    _ => InsightDirection.Flat
                };

                // Define the bar count (duration) for the new insight
                var insightBarCount = sd.InsightDurationBars; // As per previous logic

                // Get all active insights from the algorithm at the current time
                var allActiveAlgorithmInsights = algorithm.Insights.GetActiveInsights(data.Time);

                // Filter for insights matching the current symbol
                var activeInsightsForSymbol = allActiveAlgorithmInsights.Where(i => i.Symbol == symbol).ToList(); // Use ToList() to materialize for logging

                // --- DIAGNOSTIC LOGGING START ---
                var activeInsightDetails = string.Join("; ", activeInsightsForSymbol.Select(i => $"Dir={i.Direction}, Exp={i.CloseTimeUtc}"));
                LogManager.LogThrottled($"[DEBUG-{symbol}] Time: {data.Time}, TargetDir: {direction}, SignalValue: {sd.SignalDirection}, ActiveInsights: [{activeInsightDetails}]", TimeSpan.FromMinutes(60));
                // --- DIAGNOSTIC LOGGING END ---

                // Try to find an existing active insight for this symbol with the same direction
                var matchingExistingInsight = activeInsightsForSymbol.FirstOrDefault(
                    i => i.Direction == direction // Parentheses removed
                );

                // --- DIAGNOSTIC LOGGING START ---
                LogManager.LogThrottled($"[DEBUG-{symbol}] MatchingInsightFound: {(matchingExistingInsight != null)}", TimeSpan.FromMinutes(60));
                // --- DIAGNOSTIC LOGGING END ---

                if (matchingExistingInsight != null)
                {
                    // Found an active insight with the same properties. 
                    // Explicitly cancel any *other* active insights for this symbol with a different direction using the instance method.
                    var insightsToCancel = activeInsightsForSymbol.Where(i => i.Direction != direction).ToList();
                    if (insightsToCancel.Any())
                    {
                        LogManager.LogThrottled($"[INSIGHT-CANCEL-OTHERS-INSTANCE] Matching {direction} insight found for {symbol}. Calling Cancel({data.Time}) on {insightsToCancel.Count} non-matching insights.", TimeSpan.FromMinutes(60));
                        foreach (var insightToCancel in insightsToCancel)
                        {
                            LogManager.LogThrottled($"[DEBUG-{symbol}] Calling Cancel() on Insight Id: {insightToCancel.Id}, Direction: {insightToCancel.Direction}", TimeSpan.FromMinutes(60));
                            insightToCancel.Cancel(data.Time); // Call instance method
                        }
                    }
                    else
                    {
                        LogManager.LogThrottled($"[INSIGHT-SKIP] Matching {direction} insight already active for {symbol}. No other insights to cancel.", TimeSpan.FromMinutes(60));
                    }
                    // Do not emit the matching insight again, just let it continue.
                    continue; // Move to the next symbol
                }
                else
                {
                    // No matching active insight found. Explicitly cancel ALL existing active insights for this symbol using the instance method.
                    LogManager.LogThrottled($"[INSIGHT-CANCEL-ALL-INSTANCE] No matching {direction} insight found for {symbol}. Calling Cancel({data.Time}) on {activeInsightsForSymbol.Count} existing insights and emitting new.", TimeSpan.FromMinutes(60)); // Throttle to once per day

                    foreach (var insightToCancel in activeInsightsForSymbol)
                    {
                        LogManager.Log($"[DEBUG-{symbol}] Calling Cancel() on Insight Id: {insightToCancel.Id}, Direction: {insightToCancel.Direction}");
                        insightToCancel.Cancel(data.Time); // Call instance method
                        // NOTE: This modifies the local object, may not affect framework state.
                    }

                    // Create and emit the new insight.
                    var newInsight = Insight.Price(symbol, sd.InsightResolution, insightBarCount, direction);
                    insights.Add(newInsight); // Add the new insight to be returned
                }
            }

            return insights;
        }
    }
}
