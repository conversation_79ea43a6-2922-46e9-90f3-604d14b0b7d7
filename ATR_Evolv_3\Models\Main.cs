#region imports
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Globalization;
using System.Drawing;
using QuantConnect;
using QuantConnect.Algorithm.Framework;
using QuantConnect.Algorithm.Framework.Selection;
using QuantConnect.Algorithm.Framework.Alphas;
using QuantConnect.Algorithm.Framework.Portfolio;
using QuantConnect.Algorithm.Framework.Execution;
using QuantConnect.Algorithm.Framework.Risk;
using QuantConnect.Algorithm.Selection;
using QuantConnect.Parameters;
using QuantConnect.Benchmarks;
using QuantConnect.Brokerages;
using QuantConnect.Util;
using QuantConnect.Interfaces;
using QuantConnect.Algorithm;
using QuantConnect.Indicators;
using QuantConnect.Data;
using QuantConnect.Data.Consolidators;
using QuantConnect.Data.Custom;
using QuantConnect.DataSource;
using QuantConnect.Data.Fundamental;
using QuantConnect.Data.Market;
using QuantConnect.Data.UniverseSelection;
using QuantConnect.Notifications;
using QuantConnect.Orders;
using QuantConnect.Orders.Fees;
using QuantConnect.Orders.Fills;
using QuantConnect.Orders.Slippage;
using QuantConnect.Scheduling;
using QuantConnect.Securities;
using QuantConnect.Securities.Equity;
using QuantConnect.Securities.Future;
using QuantConnect.Securities.Option;
using QuantConnect.Securities.Forex;
using QuantConnect.Securities.Crypto;
using QuantConnect.Securities.Interfaces;
using QuantConnect.Storage;
using QCAlgorithmFramework = QuantConnect.Algorithm.QCAlgorithm;
using QCAlgorithmFrameworkBridge = QuantConnect.Algorithm.QCAlgorithm;
using QuantConnect.Indicators.CandlestickPatterns;
using QLNet;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.Text;
#endregion

namespace QuantConnect.Algorithm.CSharp
{
    /// <summary>
    /// Algorithmic trading strategy using DMI and ATR indicators
    /// for trend following with trailing stop management.
    /// Strategy can trade the underlying stock or its options.
    /// </summary>
    public class Atrevolv4 : QCAlgorithm
    {
        private AlphaModel _alphaModel; // Changed to base AlphaModel type
        private IPortfolioConstructionModel _portfolioModel;
        private ImmediateExecutionModel _executionModel;
        private NullRiskManagementModel _riskModel;
        private Symbol _equitySymbol;

        // Parameters exposed through config
        [Parameter("consolidation_period")]
        public int ConsolidationMinutes { get; set; } = 15; // Default to 15 minutes

        [Parameter("adx_period")]
        public int AdxPeriod { get; set; } = 10; // Default ADX period

        [Parameter("adx_threshold")]
        public decimal AdxThreshold { get; set; } = 20; // Default ADX threshold

        [Parameter("atr_multiplier")]
        public decimal AtrMultiplier { get; set; } = 5; // Default ATR multiplier

        [Parameter("atr_period")]
        public int AtrPeriod { get; set; } = 5; // Default ATR period

        [Parameter("position_size")]
        public decimal PositionSize { get; set; } = 0.1m; // Default 10% allocation for positions

        // Refactored insight duration to use bar count and resolution
        [Parameter("insight_bar_count")]
        public int InsightBarCount { get; set; } = 200; // Default 100 bars for insight duration

        [Parameter("insight_resolution")]
        public Resolution InsightResolution { get; set; } = Resolution.Daily; // Default resolution for insight bars

        // Option trading parameters
        [Parameter("trade_underlying")]
        public bool TradeUnderlying { get; set; } = true; // If true, trades the underlying instead of options

        [Parameter("min_days_to_expiration")]
        public int MinDaysToExpiration { get; set; } = 7; // Minimum days to expiration

        [Parameter("max_days_to_expiration")]
        public int MaxDaysToExpiration { get; set; } = 45; // Maximum days to expiration

        [Parameter("strike_selection_mode")]
        public int StrikeSelectionMode { get; set; } = 0; // 0: ATM, 1: ITM, 2: OTM, 3: CustomOffset

        [Parameter("strike_offset_percent")]
        public decimal StrikeOffsetPercent { get; set; } = 0.05m; // 5% offset for custom strike selection

        [Parameter("otm_contract_offset")]
        public int OtmContractOffset { get; set; } = 1; // Positive: N-th OTM contract, Negative: N-th ITM contract

        [Parameter("always_force_new_contract")]
        public bool AlwaysForceNewContract { get; set; } = false; // If true, always selects new contracts instead of reusing active ones

        [Parameter("strikes_up_down")]
        public int StrikesUpDown { get; set; } = 20; // Number of strikes up and down from ATM to include

        [Parameter("include_weeklys")]
        public bool IncludeWeeklys { get; set; } = true; // Whether to include weekly options

        // Chained Renko Parameters
        [Parameter("use-chained-renko")]
        public bool UseChainedRenko = true; // Use renko instead of time-based consolidation

        [Parameter("renko-volume-bar-size")]
        public decimal RenkoVolumeBarSize = 300000m; // Example default for VolumeRenko

        [Parameter("renko-price-bar-size")]
        public decimal RenkoPriceBarSize = 1.0m; // Example default for ClassicRenko

        [Parameter("aggregation_chunk_size")]
        public int AggregationChunkSize { get; set; } = 15; // Default to 15 bars (e.g., 15 one-minute bars for a 15-minute chunk)

        [Parameter("renko_price_bar_percentage")]
        public decimal RenkoPriceBarPercentage { get; set; } = 0.001m; // Default to 0.1% of price for Renko price bar

        [Parameter("renko_price_mode")]
        public int RenkoPriceMode { get; set; } = 3; // 0 for Fixed, 1 for Percentage, 2 for AdaptiveRange Volume, 3 for DollarBar

        [Parameter("renko_adaptive_range_levels")]
        public int RenkoAdaptiveRangeLevels { get; set; } = 20; // Default 20 levels for AdaptiveRange mode

        [Parameter("renko_lookback_days")]
        public int RenkoAdaptiveRangeLookbackDays { get; set; } = 7; // Default 7 days lookback for AdaptiveRange mode

        [Parameter("renko-dollar-bar-value")] // New parameter
        public decimal RenkoDollarBarValue { get; set; } = 1000000m;

        public override void Initialize()
        {
            UniverseSettings.DataNormalizationMode = DataNormalizationMode.Raw;


            // Set algorithm backtest start and end dates
            // SetStartDate(2025, 1, 1);
            // SetEndDate(2025, 3, 17);
            SetStartDate(2025, 2, 13);
            // SetStartDate(2025, 4, 8);
            SetEndDate(2025, 5, 13);

            // SetStartDate(2025, 2, 19);
            // SetEndDate(2025, 2, 26);

            var logWindows = new List<Tuple<DateTime, DateTime>>
            {
                // Tuple.Create(new DateTime(2021, 12, 01), new DateTime(2022, 4, 2))
                // Tuple.Create(new DateTime(2025, 1, 1), new DateTime(2025, 3, 17))
                Tuple.Create(new DateTime(2025, 2, 1), new DateTime(2025, 5, 13))
                // Tuple.Create(new DateTime(2025, 4, 8), new DateTime(2025, 5, 13))
            };

            // SetStartDate(2021, 12, 1);
            // SetEndDate(2022, 4, 1);

            // SetStartDate(2008, 1, 1);
            // SetEndDate(2008, 12, 31);

            // SetStartDate(2007, 10, 1);
            // SetEndDate(2008, 6, 1);

            LogManager.Initialize(this, logWindows);

            SetCash(100000);
            SetBrokerageModel(BrokerageName.InteractiveBrokersBrokerage, AccountType.Margin);

            // Calculate warmup period from beginning of previous year
            DateTime startDate = StartDate;
            // DateTime warmupStartDate = new DateTime(startDate.Year - 1, 1, 1);
            // DateTime warmupStartDate = startDate.AddMonths(-1);
            DateTime warmupStartDate = startDate.AddDays(-5);
            TimeSpan warmupPeriod = startDate - warmupStartDate;

            // Set warmup period to start from beginning of previous year
            SetWarmUp(warmupPeriod);

            LogManager.LogAlways($"Setting warmup period from {warmupStartDate.ToShortDateString()} to {startDate.ToShortDateString()} ({warmupPeriod.Days} days)");

            // Define the equity symbol we want to trade
            _equitySymbol = QuantConnect.Symbol.Create("NVDA", SecurityType.Equity, Market.USA);

            // Initialize charts
            InitializeCharts();

            // UniverseSettings.ExtendedMarketHours = true;
            // Trading the underlying stock directly
            var equity = AddEquity(_equitySymbol, Resolution.Second, extendedMarketHours: false);
            // equity.SetDataFilter(new EquityDataFilter());
            Securities[_equitySymbol].SetShortableProvider(new QuantConnect.Data.Shortable.InteractiveBrokersShortableProvider());

            SetBenchmark(_equitySymbol);
            // Securities[_equitySymbol].SetLeverage(10);

            // Set up the universe selection model based on whether we're trading options or the underlying
            if (TradeUnderlying)
            {
                // Create the alpha model for stock trading
                _alphaModel = new FlipFlopDmiAtrAlphaModel(
                    _equitySymbol,
                    adxPeriod: AdxPeriod,
                    atrPeriod: AtrPeriod,
                    adxThreshold: AdxThreshold,
                    multiplier: AtrMultiplier,
                    algorithm: this,
                    consolidationPeriod: TimeSpan.FromMinutes(ConsolidationMinutes), // Used for time-based consolidation when renko is disabled
                    insightBarCount: InsightBarCount,
                    insightResolution: InsightResolution,
                    // Pass Renko parameters
                    useChainedRenkoConsolidator: UseChainedRenko,
                    renkoVolumeBarSize: RenkoVolumeBarSize,
                    renkoPriceBarSize: RenkoPriceBarSize,
                    aggregationChunkSize: AggregationChunkSize, // Pass new parameter
                    renkoPriceBarPercentage: RenkoPriceBarPercentage, // Pass new Renko price percentage
                    renkoPriceMode: (RenkoPriceBarMode)RenkoPriceMode, // New
                    renkoAdaptiveRangeLevels: RenkoAdaptiveRangeLevels, // New
                    renkoAdaptiveRangeLookbackDays: RenkoAdaptiveRangeLookbackDays, // New
                    renkoDollarBarValue: RenkoDollarBarValue // New
                );

                LogManager.LogAlways($"Trading UNDERLYING {_equitySymbol} with position size {PositionSize:P0}, insight duration: {InsightBarCount} bars at {InsightResolution} resolution. Renko Volume Aggregation Chunk: {AggregationChunkSize}, Renko Price Pct: {RenkoPriceBarPercentage:P3}");
            }
            else
            {
                // Configure universe settings for options trading
                // UniverseSettings.DataNormalizationMode = DataNormalizationMode.Raw;

                // AddUniverseSelection(new EquityOptionsUniverseSelectionModel(
                //     optionSymbol:_equitySymbol,
                //     minExpiration: MinDaysToExpiration,
                //     maxExpiration: MaxDaysToExpiration,
                //     minStrike: -StrikesUpDown,
                //     maxStrike: StrikesUpDown));

                // Set up options universe selection following the QuantConnect example pattern
                AddUniverseSelection(
                    new OptionChainedUniverseSelectionModel(
                        // Create a manual universe with just NVDA
                        AddUniverse(MyUniverse),
                        // Define our option filter
                        FilterOptionUniverse
                    )
                );

                // Create the option alpha model with strike selection mode from parameters
                var strikeMode = OptionContractAlphaModel.OptionStrikeSelectionMode.ATM;
                switch (StrikeSelectionMode)
                {
                    case 0: strikeMode = OptionContractAlphaModel.OptionStrikeSelectionMode.ATM; break;
                    case 1: strikeMode = OptionContractAlphaModel.OptionStrikeSelectionMode.ITM; break;
                    case 2: strikeMode = OptionContractAlphaModel.OptionStrikeSelectionMode.OTM; break;
                    case 3: strikeMode = OptionContractAlphaModel.OptionStrikeSelectionMode.CustomOffset; break;
                }

                _alphaModel = new OptionContractAlphaModel(
                    _equitySymbol,
                    adxPeriod: AdxPeriod,
                    atrPeriod: AtrPeriod,
                    adxThreshold: AdxThreshold,
                    multiplier: AtrMultiplier,
                    algorithm: this,
                    consolidationPeriod: TimeSpan.FromMinutes(ConsolidationMinutes),    // Used for time-based consolidation when renko is disabled
                    minDaysToExpiration: MinDaysToExpiration,
                    maxDaysToExpiration: MaxDaysToExpiration,
                    strikeSelectionMode: strikeMode,
                    strikeOffsetPercent: StrikeOffsetPercent,
                    otmContractOffset: OtmContractOffset,
                    alwaysForceNewContract: AlwaysForceNewContract,
                    insightDurationBars: InsightBarCount,
                    insightResolution: InsightResolution,
                    // Pass Renko parameters
                    useChainedRenkoConsolidator: UseChainedRenko,
                    renkoVolumeBarSize: RenkoVolumeBarSize,
                    renkoPriceBarSize: RenkoPriceBarSize,
                    volumeAggregationChunkSize: AggregationChunkSize, // Pass new parameter
                    renkoPriceBarPercentage: RenkoPriceBarPercentage, // Pass new Renko price percentage
                    renkoPriceMode: (RenkoPriceBarMode)RenkoPriceMode, // New
                    renkoAdaptiveRangeLevels: RenkoAdaptiveRangeLevels, // New
                    renkoAdaptiveRangeLookbackDays: RenkoAdaptiveRangeLookbackDays, // New
                    renkoDollarBarValue: RenkoDollarBarValue // New
                    );

                LogManager.LogAlways($"Trading OPTIONS on {_equitySymbol} with position size {PositionSize:P0}, " +
                    $"Expiration {MinDaysToExpiration}-{MaxDaysToExpiration} days, Strike mode {strikeMode}, " +
                    $"OTM offset: {OtmContractOffset} strikes, " +
                    $"Force new contracts: {(AlwaysForceNewContract ? "Yes" : "No")}, " +
                    $"insight duration: {InsightBarCount} bars at {InsightResolution} resolution"); // Updated log message
            }

            // Use the margin portfolio construction model for both stock and options trading
            _executionModel = new ImmediateExecutionModel();
            _riskModel = new NullRiskManagementModel();

            // Register the models in the algorithm framework
            SetAlpha(_alphaModel);
            // SetPortfolioConstruction(_portfolioModel);
            SetPortfolioConstruction(new MaxAllocationEqualWeightingPortfolioConstructionModel(
                maxAllocationPercentage: PositionSize/*, logManager: _logManager // Removed */));
            SetExecution(_executionModel);
            SetRiskManagement(_riskModel);

            // Log initialization with parameters
            LogManager.LogAlways($"DMI+ATR FlipFlop strategy initialized with custom warmup from beginning of previous year");
            LogManager.LogAlways($"Indicator settings: {ConsolidationMinutes}-min bars, ADX period {AdxPeriod}, threshold {AdxThreshold}");
        }

        private IEnumerable<Symbol> MyUniverse(IEnumerable<BaseData> enumerable)
        {
            // Return the NVDA equity symbol for our universe
            return new[] { _equitySymbol };
        }

        /// <summary>
        /// Filter the option universe according to our strategy parameters
        /// </summary>
        private OptionFilterUniverse FilterOptionUniverse(OptionFilterUniverse universe)
        {
            LogManager.Log($"[UNIVERSE] Pre-filter option universe size: {universe.Count()} contracts");

            // First filter by expiration
            var filteredByExpiry = universe.Expiration(MinDaysToExpiration, MaxDaysToExpiration);

            LogManager.Log($"[UNIVERSE] After expiry filter ({MinDaysToExpiration}-{MaxDaysToExpiration} days): {filteredByExpiry.Count()} contracts");

            // Add weekly options if configured
            if (IncludeWeeklys)
            {
                filteredByExpiry = filteredByExpiry.IncludeWeeklys();

                LogManager.Log($"[UNIVERSE] After including weeklys: {filteredByExpiry.Count()} contracts");
            }

            // Finally filter by strikes around the current price
            var result = filteredByExpiry.Strikes(-StrikesUpDown, StrikesUpDown);

            // Only log if we're not in the warmup period
            LogManager.Log($"[UNIVERSE] Final filter result ({-StrikesUpDown} to {StrikesUpDown} strikes): {result.Count()} contracts");

            // Log a sample of what's being selected if any contracts are available
            if (result.Count() > 0)
            {
                var sample = result.OrderBy(x => x.ID.Date).ThenBy(x => x.ID.OptionRight).ThenBy(x => x.ID.StrikePrice).Take(5);
                foreach (var contract in sample)
                {
                    LogManager.Log($"[UNIVERSE] Sample contract: {contract.Symbol}, Expiry: {contract.ID.Date.ToShortDateString()}, Strike: {contract.ID.StrikePrice}, Right: {contract.ID.OptionRight}");
                }
            }

            return result;
        }

        /// <summary>
        /// Event handler that fires whenever securities are added to the algorithm
        /// </summary>
        public override void OnSecuritiesChanged(SecurityChanges changes)
        {
            if (changes.AddedSecurities.Count > 0)
            {
                LogManager.Log($"[SECURITIES] {changes.AddedSecurities.Count} securities added to algorithm");

                foreach (var security in changes.AddedSecurities)
                {
                    if (security.Symbol.SecurityType == SecurityType.Option)
                    {
                        var option = security.Symbol;
                        LogManager.Log($"[OPTION-ADDED] {option}, Strike: {option.ID.StrikePrice}, Expiry: {option.ID.Date.ToShortDateString()}, Right: {option.ID.OptionRight}");
                    }
                }
            }

            if (changes.RemovedSecurities.Count > 0)
            {
                LogManager.Log($"[SECURITIES] {changes.RemovedSecurities.Count} securities removed from algorithm");

                foreach (var security in changes.RemovedSecurities)
                {
                    if (security.Symbol.SecurityType == SecurityType.Option)
                    {
                        var option = security.Symbol;
                        LogManager.Log($"[OPTION-REMOVED] {option}, Strike: {option.ID.StrikePrice}, Expiry: {option.ID.Date.ToShortDateString()}, Right: {option.ID.OptionRight}");
                    }
                }
            }

            // Liquidate positions for securities removed from the universe
            foreach (var security in changes.RemovedSecurities)
            {
                // Only liquidate if we have an invested position in this security
                if (Portfolio.ContainsKey(security.Symbol) && Portfolio[security.Symbol].Invested)
                {
                    // Insights.Expire([security.Symbol]);
                    Liquidate(security.Symbol, "Removed from Universe");
                    LogManager.Log($"[LIQUIDATE] Liquidating {security.Symbol} as it was removed from universe");
                }
            }

            // Pass the changes to the alpha model for position management
            // if (_alphaModel is OptionContractAlphaModel optionAlphaModel)
            // {
            //     optionAlphaModel.OnSecuritiesChanged(this, changes);
            // }
        }

        /// <summary>
        /// Called when algorithm is done warming up
        /// </summary>
        public override void OnWarmupFinished()
        {
            LogManager.LogAlways("[WARMUP-COMPLETE] Warmup completed, alpha model will manage insights");
            base.OnWarmupFinished();
        }

        /// <summary>
        /// Sets up the charts used by the algorithm
        /// </summary>
        private void InitializeCharts()
        {
            // Price and Stops Chart with colored stops
            Chart priceChart = new Chart("Price and Stops");
            priceChart.AddSeries(new CandlestickSeries("Price", 0, "$"));
            // priceChart.AddSeries(new Series("Price", SeriesType.Bar, 0, "$"));

            // Add scatter plot series for trailing stops with color coding
            priceChart.AddSeries(new Series("Long Stop", SeriesType.Scatter, 0, "$")
            {
                ScatterMarkerSymbol = ScatterMarkerSymbol.Diamond,
                Color = Color.Green
            });

            priceChart.AddSeries(new Series("Short Stop", SeriesType.Scatter, 0, "$")
            {
                ScatterMarkerSymbol = ScatterMarkerSymbol.Diamond,
                Color = Color.Red
            });

            AddChart(priceChart);

            // ADX, DI, and ATR Lines Chart
            Chart adxChart = new Chart("ADX, DI, and ATR");
            adxChart.AddSeries(new Series("ADX", SeriesType.Line, "ADX"));
            adxChart.AddSeries(new Series("+DI", SeriesType.Line, "ADX"));
            adxChart.AddSeries(new Series("-DI", SeriesType.Line, "ADX"));
            adxChart.AddSeries(new Series("ATR", SeriesType.Line, "ATR")); // Add ATR series
            AddChart(adxChart);

            // Add an Options Analytics chart when trading options
            // if (!TradeUnderlying)
            // {
            //     Chart optionsChart = new Chart("Option Analytics");
            //     optionsChart.AddSeries(new Series("Delta", SeriesType.Line));
            //     optionsChart.AddSeries(new Series("Gamma", SeriesType.Line));
            //     optionsChart.AddSeries(new Series("IV", SeriesType.Line, "%"));
            //     optionsChart.AddSeries(new Series("Theta", SeriesType.Line, "$"));
            //     AddChart(optionsChart);
            // }
        }

        /// <summary>
        /// Diagnostic method to check if market data is flowing
        /// </summary>
        public override void OnData(Slice slice)
        {
            // Add diagnostic logging to check if data is flowing
            if (slice.HasData && slice.Bars.ContainsKey(_equitySymbol))
            {
                var bar = slice.Bars[_equitySymbol];
                LogManager.LogThrottled($"[DATA-FLOW] Received market data for {_equitySymbol}: Time={bar.Time}, Close={bar.Close:F2}, Volume={bar.Volume}", TimeSpan.FromMinutes(15));
            }

            // Only process during regular trading hours and only for option analytics
            if (TradeUnderlying || IsWarmingUp || !slice.HasData)
            {
                return;
            }

            // Check for option chains to plot analytics
            if (slice.OptionChains.Count > 0)
            {
                // Find the active option contract(s) we're trading
                var activePositions = Portfolio.Values
                    .Where(x => x.Invested && x.Symbol.SecurityType == SecurityType.Option)
                    .ToList();

                foreach (var position in activePositions)
                {
                    // Get the option chain for this contract's underlying
                    if (slice.OptionChains.TryGetValue(position.Symbol.Underlying, out var chain))
                    {
                        // Find the contract data in the chain
                        var contract = chain.FirstOrDefault(x => x.Symbol == position.Symbol);

                        if (contract != null)
                        {
                            // Plot the option Greeks and IV
                            Plot("Option Analytics", "Delta", contract.Greeks.Delta);
                            Plot("Option Analytics", "Gamma", contract.Greeks.Gamma);
                            Plot("Option Analytics", "IV", contract.ImpliedVolatility);
                            Plot("Option Analytics", "Theta", contract.Greeks.Theta);
                        }
                    }
                }
            }
        }
    }
}
