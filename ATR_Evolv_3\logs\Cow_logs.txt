2025-02-08 00:00:00 Launching analysis for c5dfe2e8e78b12575d8c3487d15d95dd with LEAN Engine v2.5.0.0.17126
2025-02-08 00:00:00 Accurate daily end-times now enabled by default. See more at https://qnt.co/3YHaWHL. To disable it and use legacy daily bars set self.settings.daily_precise_end_time = False.
2025-02-08 00:00:00 Algorithm starting warm up...
2025-02-08 00:00:00 LogManager: Added log window: 2025-02-01 00:00:00 to 2025-05-13 00:00:00
2025-02-08 00:00:00 LogManager Initialized.
2025-02-08 00:00:00 Setting warmup period from 2/8/2025 to 2/13/2025 (5 days)
2025-02-08 00:00:00 [ALPHA-CONFIG] Initialized with insight duration: 200 bars at Daily resolution. Renko Enabled: True
2025-02-08 00:00:00 Trading UNDERLYING NVDA with position size 100%, insight duration: 200 bars at Daily resolution. Renko Volume Aggregation Chunk: 15, <PERSON><PERSON> Price Pct: 0.070%
2025-02-08 00:00:00 [MAX-PCM] Initialized with maximum allocation: 100%, optimize rebalancing: True
2025-02-08 00:00:00 DMI+ATR FlipFlop strategy initialized with custom warmup from beginning of previous year
2025-02-08 00:00:00 Indicator settings: 15-min bars, ADX period 10, threshold 20
2025-02-08 00:00:00 [SECURITIES] 1 securities added to algorithm
2025-02-08 00:00:00 [SECURITIES-CHANGED] OnSecuritiesChanged called with 1 added, 0 removed
2025-02-08 00:00:00 [SECURITIES-CHANGED] Target underlying symbol: NVDA
2025-02-08 00:00:00 [SECURITIES-CHANGED] Processing added security: NVDA (Type: Equity)
2025-02-08 00:00:00 [ALPHA-FILTER] Adding indicators for NVDA (underlying security)
2025-02-08 00:00:00 [ALPHA_MODEL] Initializing Chained Renko for NVDA. Calculating initial first consolidator target value...
2025-02-08 00:00:00 [ALPHA_MODEL] GetPreviousDayMedianDollarValue for NVDA at 2/8/2025 12:00:00 AM
2025-02-08 00:00:00 [ALPHA_MODEL] GetPreviousDayMedianDollarValue for NVDA: Median dollar value: 1043949226.8700 from 208 chunks.
2025-02-08 00:00:00 [ALPHA_MODEL] Initial Renko params for NVDA (DollarInput): DollarValue=1043949226.8700
2025-02-08 00:00:00 [ALPHA_MODEL] GetCurrentPriceBarSize for NVDA at 2/8/2025 12:00:00 AM, Mode: DollarVolumeInput
2025-02-08 00:00:00 [ALPHA_MODEL] GetCurrentPriceBarSize: Mode is AdaptiveRange. Levels: 30, LookbackDays: 10
2025-02-08 00:00:00 [ALPHA_MODEL] GetCurrentPriceBarSize: Fetched 3120 minute bars for NVDA over the last 10 calendar days for AdaptiveRange (ending 2/8/2025 12:00:00 AM).
2025-02-08 00:00:00 [ALPHA_MODEL] GetCurrentPriceBarSize: Calculated average daily delta for NVDA: 5.02625 from 8 valid daily ranges.
2025-02-08 00:00:00 [ALPHA_MODEL] GetCurrentPriceBarSize: AdaptiveRange calculated final bar size for NVDA: 0.16754
2025-02-08 00:00:00 [ALPHA_MODEL] Initial Renko params for NVDA: PriceSize=0.1675416666666666666666666667 (determined by mode: DollarVolumeInput)
2025-02-08 00:00:00 [ALPHA_MODEL] SetupRenkoConsolidator for NVDA: Using RenkoCompatibleDollarVolumeConsolidator with CONSTANT target=$1000 (FIXED: Will always trigger consolidation)
2025-02-08 00:00:00 [CONSOLIDATOR-SETUP] Creating DebugSequentialConsolidator with order: DynamicRenkoConsolidator -> RenkoCompatibleDollarVolumeConsolidator
2025-02-08 00:00:00 [DEBUG-SEQUENTIAL-INIT] Creating DebugSequentialConsolidator: DynamicRenkoConsolidator -> RenkoCompatibleDollarVolumeConsolidator
2025-02-08 00:00:00 [DEBUG-SEQUENTIAL-INIT] Input Type: IBaseData, Output Type: VolumeBar
2025-02-08 00:00:00 [CONSOLIDATOR-SETUP] DebugSequentialConsolidator created successfully. InputType: IBaseData, OutputType: VolumeBar
2025-02-08 00:00:00 [CONSOLIDATOR-SETUP] SUCCESS: DebugSequentialConsolidator chain created for NVDA:
2025-02-08 00:00:00 [CONSOLIDATOR-SETUP]   First (receives raw data): DynamicRenkoConsolidator(0.1675416666666666666666666667)
2025-02-08 00:00:00 [CONSOLIDATOR-SETUP]   Second (receives Renko output): RenkoCompatibleDollarVolumeConsolidator(1043949226.8700)
2025-02-08 00:00:00 [CONSOLIDATOR-SETUP]   Data flow: Raw Data -> DynamicRenkoConsolidator -> RenkoCompatibleDollarVolumeConsolidator -> OnRenkoDataConsolidated
2025-02-08 00:00:00 [CONSOLIDATOR-SETUP] WATCH: Look for '[DEBUG-SEQUENTIAL-*]' and '[CONSOLIDATOR-FIX-SUCCESS]' messages to verify the fix is working!
2025-02-08 00:00:00 [ALPHA_MODEL] Scheduled daily Renko consolidator reset and recreation for NVDA at midnight.
2025-02-10 09:30:01 [ALPHA_MODEL] UpdateRenkoConsolidatorBarSizes for NVDA at 2/10/2025 12:00:00 AM
2025-02-10 09:30:01 [ALPHA_MODEL] GetPreviousDayMedianVolume for NVDA at 2/10/2025 12:00:00 AM
2025-02-10 09:30:01 [ALPHA_MODEL] GetPreviousDayMedianVolume for NVDA: Calculated median 15-min volume: 7333795.5 from 156 chunks. History bars: 2340
2025-02-10 09:30:01 [ALPHA_MODEL] GetPreviousDayMedianDollarValue for NVDA at 2/10/2025 12:00:00 AM
2025-02-10 09:30:01 [ALPHA_MODEL] GetPreviousDayMedianDollarValue for NVDA: Median dollar value: 891634941.8775 from 156 chunks.
2025-02-10 09:30:01 [ALPHA_MODEL] GetCurrentPriceBarSize for NVDA at 2/10/2025 12:00:00 AM, Mode: DollarVolumeInput
2025-02-10 09:30:01 [ALPHA_MODEL] GetCurrentPriceBarSize: Mode is AdaptiveRange. Levels: 30, LookbackDays: 10
2025-02-10 09:30:01 [ALPHA_MODEL] GetCurrentPriceBarSize: Fetched 2340 minute bars for NVDA over the last 10 calendar days for AdaptiveRange (ending 2/10/2025 12:00:00 AM).
2025-02-10 09:30:01 [ALPHA_MODEL] GetCurrentPriceBarSize: Calculated average daily delta for NVDA: 4.57667 from 6 valid daily ranges.
2025-02-10 09:30:01 [ALPHA_MODEL] GetCurrentPriceBarSize: AdaptiveRange calculated final bar size for NVDA: 0.15256
2025-02-10 09:30:01 [ALPHA_MODEL] Calculated bar sizes for NVDA: VolumeBarSize=7333795.5, DollarVolumeBarSize=891634941.8775, PriceBarSize=0.1525555555555555555555555556
2025-02-10 09:30:01 [DEBUG-UPDATE] Processing consolidator of type: DebugSequentialConsolidator for NVDA
2025-02-10 09:30:01 [DEBUG-UPDATE] Found DebugSequentialConsolidator for NVDA
2025-02-10 09:30:01 [DEBUG-UPDATE] DebugSequentialConsolidator for NVDA has First: DynamicRenkoConsolidator, Second: RenkoCompatibleDollarVolumeConsolidator
2025-02-10 09:30:01 [DEBUG-UPDATE] Processing consolidator of type: DynamicRenkoConsolidator for NVDA
2025-02-10 09:30:01 [DEBUG-UPDATE-DETAIL] Evaluating consolidator: DynamicRenkoConsolidator for NVDA. Available sizes: Volume=7333795.5, DollarVolume=891634941.8775, Price=0.1525555555555555555555555556
2025-02-10 09:30:01 [DEBUG-UPDATE-DETAIL] Branch: Renko type (actual type: DynamicRenkoConsolidator). Selected priceBarSize: 0.1525555555555555555555555556. Current: 0.1675416666666666666666666667, Change: -0.0149861111111111111111111111
2025-02-10 09:30:01 [DEBUG-UPDATE-DETAIL] For DynamicRenkoConsolidator on NVDA, chosen appropriateBarSize = 0.1525555555555555555555555556
2025-02-10 09:30:01 [DEBUG-UPDATE] UPDATING DynamicRenkoConsolidator TargetBarSize from 0.1675416666666666666666666667 to 0.1525555555555555555555555556 for NVDA
2025-02-10 09:30:01 [DEBUG-UPDATE] Successfully updated DynamicRenkoConsolidator for NVDA
2025-02-10 09:30:01 [DEBUG-UPDATE] Processing consolidator of type: RenkoCompatibleDollarVolumeConsolidator for NVDA
2025-02-10 09:30:01 [DEBUG-UPDATE-DETAIL] RenkoCompatibleDollarVolumeConsolidator for NVDA uses constant target ($1000). Skipping daily update.
2025-02-10 09:30:01 [ALPHA_MODEL] Successfully updated all consolidators for NVDA
2025-02-10 09:30:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 9:30:00 AM
2025-02-10 09:30:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 9:30:00 AM, Close=130.02, Volume=2666961
2025-02-10 09:30:03 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:30:00 AM
2025-02-10 09:30:03 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=130.13, Volume=0
2025-02-10 09:30:03 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:30:08 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:30:02 AM
2025-02-10 09:30:08 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=130.28, Volume=0
2025-02-10 09:30:08 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:30:15 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:30:07 AM
2025-02-10 09:30:15 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=130.44, Volume=0
2025-02-10 09:30:15 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:30:35 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:30:14 AM
2025-02-10 09:30:35 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=130.59, Volume=0
2025-02-10 09:30:35 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:30:49 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:30:34 AM
2025-02-10 09:30:49 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=130.74, Volume=0
2025-02-10 09:30:49 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:31:07 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:30:48 AM
2025-02-10 09:31:07 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=130.89, Volume=0
2025-02-10 09:31:07 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:31:13 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:31:06 AM
2025-02-10 09:31:13 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.05, Volume=0
2025-02-10 09:31:13 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:31:15 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:31:12 AM
2025-02-10 09:31:15 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.20, Volume=0
2025-02-10 09:31:15 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:31:32 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:31:14 AM
2025-02-10 09:31:32 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.35, Volume=0
2025-02-10 09:31:32 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:31:38 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:31:31 AM
2025-02-10 09:31:38 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.50, Volume=0
2025-02-10 09:31:38 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:31:56 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:31:37 AM
2025-02-10 09:31:56 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.66, Volume=0
2025-02-10 09:31:56 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:33:09 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:31:55 AM
2025-02-10 09:33:09 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.35, Volume=0
2025-02-10 09:33:09 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:33:25 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:33:08 AM
2025-02-10 09:33:25 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.66, Volume=0
2025-02-10 09:33:25 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:33:44 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:33:24 AM
2025-02-10 09:33:44 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.35, Volume=0
2025-02-10 09:33:44 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:34:05 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:33:43 AM
2025-02-10 09:34:05 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.66, Volume=0
2025-02-10 09:34:05 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:34:37 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:34:04 AM
2025-02-10 09:34:37 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.81, Volume=0
2025-02-10 09:34:37 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:35:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 9:35:00 AM
2025-02-10 09:35:07 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:34:36 AM
2025-02-10 09:35:07 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.96, Volume=0
2025-02-10 09:35:07 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:35:22 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:35:06 AM
2025-02-10 09:35:22 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.11, Volume=0
2025-02-10 09:35:22 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:35:38 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:35:21 AM
2025-02-10 09:35:38 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.27, Volume=0
2025-02-10 09:35:38 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:36:04 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:35:37 AM
2025-02-10 09:36:04 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.42, Volume=0
2025-02-10 09:36:04 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:36:12 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:36:03 AM
2025-02-10 09:36:12 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.57, Volume=0
2025-02-10 09:36:12 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:36:21 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:36:11 AM
2025-02-10 09:36:21 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.72, Volume=0
2025-02-10 09:36:21 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:37:04 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:36:20 AM
2025-02-10 09:37:04 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.88, Volume=0
2025-02-10 09:37:04 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:37:22 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:37:03 AM
2025-02-10 09:37:22 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.03, Volume=0
2025-02-10 09:37:22 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:38:07 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:37:21 AM
2025-02-10 09:38:07 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.18, Volume=0
2025-02-10 09:38:07 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:38:22 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:38:06 AM
2025-02-10 09:38:22 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.33, Volume=0
2025-02-10 09:38:22 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:38:35 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:38:21 AM
2025-02-10 09:38:35 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.49, Volume=0
2025-02-10 09:38:35 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:38:45 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:38:34 AM
2025-02-10 09:38:45 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.64, Volume=0
2025-02-10 09:38:45 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:39:32 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:38:44 AM
2025-02-10 09:39:32 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.79, Volume=0
2025-02-10 09:39:32 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:39:48 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:39:31 AM
2025-02-10 09:39:48 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 09:39:48 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:40:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 9:40:00 AM
2025-02-10 09:40:06 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:39:47 AM
2025-02-10 09:40:06 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.64, Volume=0
2025-02-10 09:40:06 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:40:22 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:40:05 AM
2025-02-10 09:40:22 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.49, Volume=0
2025-02-10 09:40:22 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:40:28 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:40:21 AM
2025-02-10 09:40:28 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.33, Volume=0
2025-02-10 09:40:28 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:40:39 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:40:27 AM
2025-02-10 09:40:39 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.18, Volume=0
2025-02-10 09:40:39 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:41:26 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:40:38 AM
2025-02-10 09:41:26 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.03, Volume=0
2025-02-10 09:41:26 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:41:40 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:41:25 AM
2025-02-10 09:41:40 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.88, Volume=0
2025-02-10 09:41:40 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:41:48 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:41:39 AM
2025-02-10 09:41:48 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.72, Volume=0
2025-02-10 09:41:48 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:42:10 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:41:47 AM
2025-02-10 09:42:10 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.03, Volume=0
2025-02-10 09:42:10 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:42:21 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:42:09 AM
2025-02-10 09:42:21 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.18, Volume=0
2025-02-10 09:42:21 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:42:26 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:42:20 AM
2025-02-10 09:42:26 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.33, Volume=0
2025-02-10 09:42:26 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:42:53 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:42:25 AM
2025-02-10 09:42:53 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.49, Volume=0
2025-02-10 09:42:53 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:44:46 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:42:52 AM
2025-02-10 09:44:46 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.64, Volume=0
2025-02-10 09:44:46 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:45:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 9:45:00 AM
2025-02-10 09:45:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 9:45:00 AM, Close=133.40, Volume=17678
2025-02-10 09:45:03 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:44:45 AM
2025-02-10 09:45:03 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.33, Volume=0
2025-02-10 09:45:03 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:45:26 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:45:02 AM
2025-02-10 09:45:26 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.64, Volume=0
2025-02-10 09:45:26 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:45:29 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:45:25 AM
2025-02-10 09:45:29 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.79, Volume=0
2025-02-10 09:45:29 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:46:13 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:45:28 AM
2025-02-10 09:46:13 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 09:46:13 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:46:33 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:46:12 AM
2025-02-10 09:46:33 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.10, Volume=0
2025-02-10 09:46:33 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:46:50 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:46:32 AM
2025-02-10 09:46:50 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 09:46:50 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:47:09 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:46:49 AM
2025-02-10 09:47:09 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.40, Volume=0
2025-02-10 09:47:09 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:49:48 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:47:08 AM
2025-02-10 09:49:48 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.10, Volume=0
2025-02-10 09:49:48 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:50:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 9:50:00 AM
2025-02-10 09:50:03 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:49:47 AM
2025-02-10 09:50:03 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 09:50:03 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:50:11 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:50:02 AM
2025-02-10 09:50:11 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.79, Volume=0
2025-02-10 09:50:11 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:50:20 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:50:10 AM
2025-02-10 09:50:20 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.64, Volume=0
2025-02-10 09:50:20 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:50:42 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:50:19 AM
2025-02-10 09:50:42 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 09:50:42 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:50:47 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:50:41 AM
2025-02-10 09:50:47 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.10, Volume=0
2025-02-10 09:50:47 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:50:58 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:50:46 AM
2025-02-10 09:50:58 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 09:50:58 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:51:34 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:50:57 AM
2025-02-10 09:51:34 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 09:51:34 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:52:08 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:51:33 AM
2025-02-10 09:52:08 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.79, Volume=0
2025-02-10 09:52:08 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:52:39 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:52:07 AM
2025-02-10 09:52:39 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.64, Volume=0
2025-02-10 09:52:39 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:53:06 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:52:38 AM
2025-02-10 09:53:06 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 09:53:06 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:53:19 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:53:05 AM
2025-02-10 09:53:19 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.10, Volume=0
2025-02-10 09:53:19 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:54:26 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:53:18 AM
2025-02-10 09:54:26 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 09:54:26 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:55:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 9:55:00 AM
2025-02-10 09:55:10 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:54:25 AM
2025-02-10 09:55:10 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 09:55:10 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:56:14 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:55:09 AM
2025-02-10 09:56:14 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 09:56:14 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:57:13 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:56:13 AM
2025-02-10 09:57:13 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 09:57:13 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:57:26 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:57:12 AM
2025-02-10 09:57:26 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.79, Volume=0
2025-02-10 09:57:26 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:58:43 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:57:25 AM
2025-02-10 09:58:43 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.64, Volume=0
2025-02-10 09:58:43 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 09:59:54 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:58:42 AM
2025-02-10 09:59:54 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 09:59:54 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:00:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:00:00 AM
2025-02-10 10:00:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 10:00:00 AM, Close=133.81, Volume=23771
2025-02-10 10:01:29 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:59:53 AM
2025-02-10 10:01:29 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.10, Volume=0
2025-02-10 10:01:29 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:01:40 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:01:28 AM
2025-02-10 10:01:40 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 10:01:40 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:02:14 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:01:39 AM
2025-02-10 10:02:14 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 10:02:14 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:02:22 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:02:13 AM
2025-02-10 10:02:22 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.79, Volume=0
2025-02-10 10:02:22 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:02:50 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:02:21 AM
2025-02-10 10:02:50 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.64, Volume=0
2025-02-10 10:02:50 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:05:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:05:00 AM
2025-02-10 10:05:25 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:02:49 AM
2025-02-10 10:05:25 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 10:05:25 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:08:31 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:05:24 AM
2025-02-10 10:08:31 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.10, Volume=0
2025-02-10 10:08:31 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:08:45 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:08:30 AM
2025-02-10 10:08:45 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 10:08:45 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:10:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:10:00 AM
2025-02-10 10:13:53 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:08:44 AM
2025-02-10 10:13:53 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.40, Volume=0
2025-02-10 10:13:53 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:15:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:15:00 AM
2025-02-10 10:15:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 10:15:00 AM, Close=134.33, Volume=6005
2025-02-10 10:15:30 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:13:52 AM
2025-02-10 10:15:30 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.10, Volume=0
2025-02-10 10:15:30 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:18:22 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:15:29 AM
2025-02-10 10:18:22 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 10:18:22 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:18:45 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:18:21 AM
2025-02-10 10:18:45 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 10:18:45 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:19:35 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:18:44 AM
2025-02-10 10:19:35 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.40, Volume=0
2025-02-10 10:19:35 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:19:46 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:19:34 AM
2025-02-10 10:19:46 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.55, Volume=0
2025-02-10 10:19:46 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:20:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:20:00 AM
2025-02-10 10:23:22 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:19:45 AM
2025-02-10 10:23:22 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.71, Volume=0
2025-02-10 10:23:22 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:25:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:25:00 AM
2025-02-10 10:25:12 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:23:21 AM
2025-02-10 10:25:12 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.40, Volume=0
2025-02-10 10:25:12 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:30:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:30:00 AM
2025-02-10 10:30:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 10:30:00 AM, Close=134.46, Volume=14420
2025-02-10 10:33:47 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:25:11 AM
2025-02-10 10:33:47 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.71, Volume=0
2025-02-10 10:33:47 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:35:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:35:00 AM
2025-02-10 10:36:34 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:33:46 AM
2025-02-10 10:36:34 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.40, Volume=0
2025-02-10 10:36:34 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:39:21 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:36:33 AM
2025-02-10 10:39:21 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.71, Volume=0
2025-02-10 10:39:21 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:40:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:40:00 AM
2025-02-10 10:43:24 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:39:20 AM
2025-02-10 10:43:24 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.40, Volume=0
2025-02-10 10:43:24 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:45:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:45:00 AM
2025-02-10 10:45:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 10:45:00 AM, Close=134.42, Volume=5973
2025-02-10 10:46:28 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:43:23 AM
2025-02-10 10:46:28 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 10:46:28 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:48:52 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:46:27 AM
2025-02-10 10:48:52 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.55, Volume=0
2025-02-10 10:48:52 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:50:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:50:00 AM
2025-02-10 10:50:37 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:48:51 AM
2025-02-10 10:50:37 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 10:50:37 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:52:52 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:50:36 AM
2025-02-10 10:52:52 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.10, Volume=0
2025-02-10 10:52:52 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:53:36 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:52:51 AM
2025-02-10 10:53:36 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 10:53:36 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 10:55:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:55:00 AM
2025-02-10 10:58:21 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:53:35 AM
2025-02-10 10:58:21 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 10:58:21 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 11:00:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 11:00:00 AM
2025-02-10 11:00:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 11:00:00 AM, Close=134.02, Volume=16048
2025-02-10 11:00:29 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:58:20 AM
2025-02-10 11:00:29 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 11:00:29 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 11:05:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 11:05:00 AM
2025-02-10 11:10:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 11:10:00 AM
2025-02-10 11:15:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 11:15:00 AM
2025-02-10 11:15:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 11:15:00 AM, Close=134.09, Volume=13000
2025-02-10 11:20:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 11:20:00 AM
2025-02-10 11:20:11 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 11:00:28 AM
2025-02-10 11:20:11 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.79, Volume=0
2025-02-10 11:20:11 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 11:25:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 11:25:00 AM
2025-02-10 11:26:22 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 11:20:10 AM
2025-02-10 11:26:22 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.64, Volume=0
2025-02-10 11:26:22 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 11:27:23 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 11:26:21 AM
2025-02-10 11:27:23 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.49, Volume=0
2025-02-10 11:27:23 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 11:29:18 Processing algorithm warm-up request 49%...
2025-02-10 11:30:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 11:30:00 AM
2025-02-10 11:30:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 11:30:00 AM, Close=133.51, Volume=0
2025-02-10 11:31:15 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 11:27:22 AM
2025-02-10 11:31:15 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.79, Volume=0
2025-02-10 11:31:15 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 11:35:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 11:35:00 AM
2025-02-10 11:40:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 11:40:00 AM
2025-02-10 11:42:53 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 11:31:14 AM
2025-02-10 11:42:53 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.49, Volume=0
2025-02-10 11:42:53 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 11:43:41 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 11:42:52 AM
2025-02-10 11:43:41 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.33, Volume=0
2025-02-10 11:43:41 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 11:45:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 11:45:00 AM
2025-02-10 11:45:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 11:45:00 AM, Close=133.38, Volume=7980
2025-02-10 11:46:19 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 11:43:40 AM
2025-02-10 11:46:19 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.18, Volume=0
2025-02-10 11:46:19 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 11:46:59 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 11:46:18 AM
2025-02-10 11:46:59 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.03, Volume=0
2025-02-10 11:46:59 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 11:50:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 11:50:00 AM
2025-02-10 11:55:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 11:55:00 AM
2025-02-10 11:56:28 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 11:46:58 AM
2025-02-10 11:56:28 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.33, Volume=0
2025-02-10 11:56:28 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 11:56:59 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 11:56:27 AM
2025-02-10 11:56:59 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.49, Volume=0
2025-02-10 11:56:59 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 12:00:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 12:00:00 PM
2025-02-10 12:00:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 12:00:00 PM, Close=133.49, Volume=4159
2025-02-10 12:00:41 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 11:56:58 AM
2025-02-10 12:00:41 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.64, Volume=0
2025-02-10 12:00:41 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 12:05:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 12:05:00 PM
2025-02-10 12:10:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 12:10:00 PM
2025-02-10 12:10:42 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 12:00:40 PM
2025-02-10 12:10:42 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.33, Volume=0
2025-02-10 12:10:42 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 12:15:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 12:15:00 PM
2025-02-10 12:15:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 12:15:00 PM, Close=133.39, Volume=12272
2025-02-10 12:20:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 12:20:00 PM
2025-02-10 12:24:11 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 12:10:41 PM
2025-02-10 12:24:11 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.64, Volume=0
2025-02-10 12:24:11 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 12:24:26 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 12:24:10 PM
2025-02-10 12:24:26 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.79, Volume=0
2025-02-10 12:24:26 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 12:24:46 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 12:24:25 PM
2025-02-10 12:24:46 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 12:24:46 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 12:25:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 12:25:00 PM
2025-02-10 12:26:26 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 12:24:45 PM
2025-02-10 12:26:26 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.10, Volume=0
2025-02-10 12:26:26 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 12:30:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 12:30:00 PM
2025-02-10 12:30:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 12:30:00 PM, Close=134.04, Volume=5614
2025-02-10 12:35:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 12:35:00 PM
2025-02-10 12:37:06 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 12:26:25 PM
2025-02-10 12:37:06 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 12:37:06 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 12:40:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 12:40:00 PM
2025-02-10 12:45:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 12:45:00 PM
2025-02-10 12:45:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 12:45:00 PM, Close=134.31, Volume=6366
2025-02-10 12:50:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 12:50:00 PM
2025-02-10 12:50:41 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 12:37:05 PM
2025-02-10 12:50:41 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.40, Volume=0
2025-02-10 12:50:41 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 12:55:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 12:55:00 PM
2025-02-10 13:00:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 1:00:00 PM
2025-02-10 13:00:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 1:00:00 PM, Close=134.30, Volume=16610
2025-02-10 13:05:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 1:05:00 PM
2025-02-10 13:10:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 1:10:00 PM
2025-02-10 13:15:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 1:15:00 PM
2025-02-10 13:15:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 1:15:00 PM, Close=134.32, Volume=1798
2025-02-10 13:20:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 1:20:00 PM
2025-02-10 13:25:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 1:25:00 PM
2025-02-10 13:30:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 1:30:00 PM
2025-02-10 13:30:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 1:30:00 PM, Close=134.32, Volume=9060
2025-02-10 13:33:18 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 12:50:40 PM
2025-02-10 13:33:18 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.55, Volume=0
2025-02-10 13:33:18 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 13:35:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 1:35:00 PM
2025-02-10 13:40:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 1:40:00 PM
2025-02-10 13:45:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 1:45:00 PM
2025-02-10 13:45:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 1:45:00 PM, Close=134.42, Volume=0
2025-02-10 13:46:32 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 1:33:17 PM
2025-02-10 13:46:32 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 13:46:32 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 13:50:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 1:50:00 PM
2025-02-10 13:55:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 1:55:00 PM
2025-02-10 14:00:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 2:00:00 PM
2025-02-10 14:00:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 2:00:00 PM, Close=134.33, Volume=10526
2025-02-10 14:05:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 2:05:00 PM
2025-02-10 14:10:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 2:10:00 PM
2025-02-10 14:12:06 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 1:46:31 PM
2025-02-10 14:12:06 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.55, Volume=0
2025-02-10 14:12:06 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 14:15:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 2:15:00 PM
2025-02-10 14:15:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 2:15:00 PM, Close=134.51, Volume=1325
2025-02-10 14:20:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 2:20:00 PM
2025-02-10 14:25:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 2:25:00 PM
2025-02-10 14:30:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 2:30:00 PM
2025-02-10 14:30:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 2:30:00 PM, Close=134.52, Volume=1170
2025-02-10 14:35:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 2:35:00 PM
2025-02-10 14:40:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 2:40:00 PM
2025-02-10 14:45:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 2:45:00 PM
2025-02-10 14:45:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 2:45:00 PM, Close=134.37, Volume=7665
2025-02-10 14:45:34 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 2:12:05 PM
2025-02-10 14:45:34 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 14:45:34 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 14:50:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 2:50:00 PM
2025-02-10 14:55:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 2:55:00 PM
2025-02-10 14:56:07 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 2:45:33 PM
2025-02-10 14:56:07 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.55, Volume=0
2025-02-10 14:56:07 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 15:00:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 3:00:00 PM
2025-02-10 15:00:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 3:00:00 PM, Close=134.41, Volume=16711
2025-02-10 15:05:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 3:05:00 PM
2025-02-10 15:09:42 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 2:56:06 PM
2025-02-10 15:09:42 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.71, Volume=0
2025-02-10 15:09:42 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 15:10:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 3:10:00 PM
2025-02-10 15:11:05 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 3:09:41 PM
2025-02-10 15:11:05 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.86, Volume=0
2025-02-10 15:11:05 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 15:15:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 3:15:00 PM
2025-02-10 15:15:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 3:15:00 PM, Close=134.98, Volume=5352
2025-02-10 15:20:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 3:20:00 PM
2025-02-10 15:23:31 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 3:11:04 PM
2025-02-10 15:23:31 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.55, Volume=0
2025-02-10 15:23:31 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 15:25:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 3:25:00 PM
2025-02-10 15:30:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 3:30:00 PM
2025-02-10 15:30:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 3:30:00 PM, Close=134.49, Volume=14577
2025-02-10 15:30:49 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 3:23:30 PM
2025-02-10 15:30:49 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.40, Volume=0
2025-02-10 15:30:49 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 15:35:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 3:35:00 PM
2025-02-10 15:39:23 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 3:30:48 PM
2025-02-10 15:39:23 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 15:39:23 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 15:40:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 3:40:00 PM
2025-02-10 15:45:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 3:45:00 PM
2025-02-10 15:45:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 3:45:00 PM, Close=134.23, Volume=6371
2025-02-10 15:45:46 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 3:39:22 PM
2025-02-10 15:45:46 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.10, Volume=0
2025-02-10 15:45:46 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 15:46:32 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 3:45:45 PM
2025-02-10 15:46:32 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 15:46:32 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 15:50:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 3:50:00 PM
2025-02-10 15:50:53 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 3:46:31 PM
2025-02-10 15:50:53 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.79, Volume=0
2025-02-10 15:50:53 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 15:53:54 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 3:50:52 PM
2025-02-10 15:53:54 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.10, Volume=0
2025-02-10 15:53:54 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 15:54:41 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 3:53:53 PM
2025-02-10 15:54:41 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.25, Volume=0
2025-02-10 15:54:41 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 15:55:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 3:55:00 PM
2025-02-10 15:55:14 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 3:54:40 PM
2025-02-10 15:55:14 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.94, Volume=0
2025-02-10 15:55:14 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 15:57:25 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 3:55:13 PM
2025-02-10 15:57:25 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.79, Volume=0
2025-02-10 15:57:25 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 15:58:07 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 3:57:24 PM
2025-02-10 15:58:07 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.64, Volume=0
2025-02-10 15:58:07 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 15:59:07 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 3:58:06 PM
2025-02-10 15:59:07 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.49, Volume=0
2025-02-10 15:59:07 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-10 16:00:00 [OPT-UPDATE] Market closed for underlying NVDA. Skipping update.
2025-02-11 09:30:01 [ALPHA_MODEL] UpdateRenkoConsolidatorBarSizes for NVDA at 2/11/2025 12:00:00 AM
2025-02-11 09:30:01 [ALPHA_MODEL] GetPreviousDayMedianVolume for NVDA at 2/11/2025 12:00:00 AM
2025-02-11 09:30:01 [ALPHA_MODEL] GetPreviousDayMedianVolume for NVDA: Calculated median 15-min volume: 6642244.5 from 156 chunks. History bars: 2340
2025-02-11 09:30:01 [ALPHA_MODEL] GetPreviousDayMedianDollarValue for NVDA at 2/11/2025 12:00:00 AM
2025-02-11 09:30:01 [ALPHA_MODEL] GetPreviousDayMedianDollarValue for NVDA: Median dollar value: 825423050.3335 from 156 chunks.
2025-02-11 09:30:01 [ALPHA_MODEL] GetCurrentPriceBarSize for NVDA at 2/11/2025 12:00:00 AM, Mode: DollarVolumeInput
2025-02-11 09:30:01 [ALPHA_MODEL] GetCurrentPriceBarSize: Mode is AdaptiveRange. Levels: 30, LookbackDays: 10
2025-02-11 09:30:01 [ALPHA_MODEL] GetCurrentPriceBarSize: Fetched 2340 minute bars for NVDA over the last 10 calendar days for AdaptiveRange (ending 2/11/2025 12:00:00 AM).
2025-02-11 09:30:01 [ALPHA_MODEL] GetCurrentPriceBarSize: Calculated average daily delta for NVDA: 3.85417 from 6 valid daily ranges.
2025-02-11 09:30:01 [ALPHA_MODEL] GetCurrentPriceBarSize: AdaptiveRange calculated final bar size for NVDA: 0.12847
2025-02-11 09:30:01 [ALPHA_MODEL] Calculated bar sizes for NVDA: VolumeBarSize=6642244.5, DollarVolumeBarSize=825423050.3335, PriceBarSize=0.1284722222222222222222222222
2025-02-11 09:30:01 [DEBUG-UPDATE] Processing consolidator of type: DebugSequentialConsolidator for NVDA
2025-02-11 09:30:01 [DEBUG-UPDATE] Found DebugSequentialConsolidator for NVDA
2025-02-11 09:30:01 [DEBUG-UPDATE] DebugSequentialConsolidator for NVDA has First: DynamicRenkoConsolidator, Second: RenkoCompatibleDollarVolumeConsolidator
2025-02-11 09:30:01 [DEBUG-UPDATE] Processing consolidator of type: DynamicRenkoConsolidator for NVDA
2025-02-11 09:30:01 [DEBUG-UPDATE-DETAIL] Evaluating consolidator: DynamicRenkoConsolidator for NVDA. Available sizes: Volume=6642244.5, DollarVolume=825423050.3335, Price=0.1284722222222222222222222222
2025-02-11 09:30:01 [DEBUG-UPDATE-DETAIL] Branch: Renko type (actual type: DynamicRenkoConsolidator). Selected priceBarSize: 0.1284722222222222222222222222. Current: 0.1525555555555555555555555556, Change: -0.0240833333333333333333333334
2025-02-11 09:30:01 [DEBUG-UPDATE-DETAIL] For DynamicRenkoConsolidator on NVDA, chosen appropriateBarSize = 0.1284722222222222222222222222
2025-02-11 09:30:01 [DEBUG-UPDATE] UPDATING DynamicRenkoConsolidator TargetBarSize from 0.1525555555555555555555555556 to 0.1284722222222222222222222222 for NVDA
2025-02-11 09:30:01 [DEBUG-UPDATE] Successfully updated DynamicRenkoConsolidator for NVDA
2025-02-11 09:30:01 [DEBUG-UPDATE] Processing consolidator of type: RenkoCompatibleDollarVolumeConsolidator for NVDA
2025-02-11 09:30:01 [DEBUG-UPDATE-DETAIL] RenkoCompatibleDollarVolumeConsolidator for NVDA uses constant target ($1000). Skipping daily update.
2025-02-11 09:30:01 [ALPHA_MODEL] Successfully updated all consolidators for NVDA
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/11/2025 9:30:00 AM
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 3:59:06 PM
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.36, Volume=0
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:30:00 AM
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.23, Volume=0
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:30:00 AM
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.10, Volume=0
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:30:00 AM
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.97, Volume=0
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:30:00 AM
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.84, Volume=0
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:30:00 AM
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.72, Volume=0
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:30:00 AM
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.59, Volume=0
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:30:00 AM
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.46, Volume=0
2025-02-11 09:30:01 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:30:01 [DATA-FLOW] Received market data for NVDA: Time=2/11/2025 9:30:00 AM, Close=132.35, Volume=58891
2025-02-11 09:30:24 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:30:00 AM
2025-02-11 09:30:24 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.72, Volume=0
2025-02-11 09:30:24 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:30:35 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:30:23 AM
2025-02-11 09:30:35 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.84, Volume=0
2025-02-11 09:30:35 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:30:52 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:30:34 AM
2025-02-11 09:30:52 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.97, Volume=0
2025-02-11 09:30:52 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:31:03 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:30:51 AM
2025-02-11 09:31:03 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.10, Volume=0
2025-02-11 09:31:03 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:31:06 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:31:02 AM
2025-02-11 09:31:06 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.84, Volume=0
2025-02-11 09:31:06 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:31:09 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:31:05 AM
2025-02-11 09:31:09 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.72, Volume=0
2025-02-11 09:31:09 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:31:11 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:31:08 AM
2025-02-11 09:31:11 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.59, Volume=0
2025-02-11 09:31:11 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:31:12 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:31:10 AM
2025-02-11 09:31:12 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.46, Volume=0
2025-02-11 09:31:12 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:31:27 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:31:11 AM
2025-02-11 09:31:27 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.33, Volume=0
2025-02-11 09:31:27 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:31:43 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:31:26 AM
2025-02-11 09:31:43 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.20, Volume=0
2025-02-11 09:31:43 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:31:45 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:31:42 AM
2025-02-11 09:31:45 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.07, Volume=0
2025-02-11 09:31:45 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:32:04 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:31:44 AM
2025-02-11 09:32:04 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.33, Volume=0
2025-02-11 09:32:04 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:32:13 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:32:03 AM
2025-02-11 09:32:13 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.07, Volume=0
2025-02-11 09:32:13 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:32:29 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:32:12 AM
2025-02-11 09:32:29 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.33, Volume=0
2025-02-11 09:32:29 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:32:59 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:32:28 AM
2025-02-11 09:32:59 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.46, Volume=0
2025-02-11 09:32:59 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:33:02 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:32:58 AM
2025-02-11 09:33:02 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.59, Volume=0
2025-02-11 09:33:02 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:33:05 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:33:01 AM
2025-02-11 09:33:05 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.72, Volume=0
2025-02-11 09:33:05 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:33:08 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:33:04 AM
2025-02-11 09:33:08 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.84, Volume=0
2025-02-11 09:33:08 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:33:16 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:33:07 AM
2025-02-11 09:33:16 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.59, Volume=0
2025-02-11 09:33:16 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:33:39 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:33:15 AM
2025-02-11 09:33:39 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.84, Volume=0
2025-02-11 09:33:39 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:34:08 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:33:38 AM
2025-02-11 09:34:08 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.59, Volume=0
2025-02-11 09:34:08 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:34:33 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:34:07 AM
2025-02-11 09:34:33 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.84, Volume=0
2025-02-11 09:34:33 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:34:38 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:34:32 AM
2025-02-11 09:34:38 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.97, Volume=0
2025-02-11 09:34:38 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:34:46 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:34:37 AM
2025-02-11 09:34:46 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.10, Volume=0
2025-02-11 09:34:46 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:35:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/11/2025 9:35:00 AM
2025-02-11 09:35:03 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:34:45 AM
2025-02-11 09:35:03 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.23, Volume=0
2025-02-11 09:35:03 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:35:12 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:35:02 AM
2025-02-11 09:35:12 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.97, Volume=0
2025-02-11 09:35:12 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:35:13 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:35:11 AM
2025-02-11 09:35:13 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.84, Volume=0
2025-02-11 09:35:13 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:35:24 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:35:12 AM
2025-02-11 09:35:24 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.10, Volume=0
2025-02-11 09:35:24 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:35:48 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:35:23 AM
2025-02-11 09:35:48 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.23, Volume=0
2025-02-11 09:35:48 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:35:50 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:35:47 AM
2025-02-11 09:35:50 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.36, Volume=0
2025-02-11 09:35:50 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:36:00 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:35:49 AM
2025-02-11 09:36:00 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.49, Volume=0
2025-02-11 09:36:00 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:36:27 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:35:59 AM
2025-02-11 09:36:27 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.61, Volume=0
2025-02-11 09:36:27 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:36:50 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:36:26 AM
2025-02-11 09:36:50 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.74, Volume=0
2025-02-11 09:36:50 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:37:23 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:36:49 AM
2025-02-11 09:37:23 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.87, Volume=0
2025-02-11 09:37:23 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:38:01 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:37:22 AM
2025-02-11 09:38:01 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.61, Volume=0
2025-02-11 09:38:01 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:38:12 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:38:00 AM
2025-02-11 09:38:12 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.87, Volume=0
2025-02-11 09:38:12 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:38:28 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:38:11 AM
2025-02-11 09:38:28 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.00, Volume=0
2025-02-11 09:38:28 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:39:07 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:38:27 AM
2025-02-11 09:39:07 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.74, Volume=0
2025-02-11 09:39:07 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:39:33 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:39:06 AM
2025-02-11 09:39:33 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.00, Volume=0
2025-02-11 09:39:33 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:40:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/11/2025 9:40:00 AM
2025-02-11 09:40:09 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:39:32 AM
2025-02-11 09:40:09 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.13, Volume=0
2025-02-11 09:40:09 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:40:16 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:40:08 AM
2025-02-11 09:40:16 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.26, Volume=0
2025-02-11 09:40:16 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:42:01 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:40:15 AM
2025-02-11 09:42:01 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.00, Volume=0
2025-02-11 09:42:01 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:42:12 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:42:00 AM
2025-02-11 09:42:12 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.87, Volume=0
2025-02-11 09:42:12 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:42:36 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:42:11 AM
2025-02-11 09:42:36 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.74, Volume=0
2025-02-11 09:42:36 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:43:30 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:42:35 AM
2025-02-11 09:43:30 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.61, Volume=0
2025-02-11 09:43:30 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:44:31 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:43:29 AM
2025-02-11 09:44:31 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.87, Volume=0
2025-02-11 09:44:31 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:44:38 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:44:30 AM
2025-02-11 09:44:38 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.00, Volume=0
2025-02-11 09:44:38 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:45:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/11/2025 9:45:00 AM
2025-02-11 09:45:01 [DATA-FLOW] Received market data for NVDA: Time=2/11/2025 9:45:00 AM, Close=133.95, Volume=14223
2025-02-11 09:45:19 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:44:37 AM
2025-02-11 09:45:19 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.13, Volume=0
2025-02-11 09:45:19 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:45:42 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:45:18 AM
2025-02-11 09:45:42 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.26, Volume=0
2025-02-11 09:45:42 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:46:22 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:45:41 AM
2025-02-11 09:46:22 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.00, Volume=0
2025-02-11 09:46:22 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:47:08 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:46:21 AM
2025-02-11 09:47:08 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.87, Volume=0
2025-02-11 09:47:08 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:50:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/11/2025 9:50:00 AM
2025-02-11 09:51:36 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:47:07 AM
2025-02-11 09:51:36 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.74, Volume=0
2025-02-11 09:51:36 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:52:25 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:51:35 AM
2025-02-11 09:52:25 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.00, Volume=0
2025-02-11 09:52:25 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:52:29 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:52:24 AM
2025-02-11 09:52:29 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.13, Volume=0
2025-02-11 09:52:29 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:55:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/11/2025 9:55:00 AM
2025-02-11 09:56:21 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:52:28 AM
2025-02-11 09:56:21 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.26, Volume=0
2025-02-11 09:56:21 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:56:27 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:56:20 AM
2025-02-11 09:56:27 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.39, Volume=0
2025-02-11 09:56:27 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:57:18 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:56:26 AM
2025-02-11 09:57:18 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.13, Volume=0
2025-02-11 09:57:18 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:57:38 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:57:17 AM
2025-02-11 09:57:38 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.00, Volume=0
2025-02-11 09:57:38 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:57:58 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:57:37 AM
2025-02-11 09:57:58 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.87, Volume=0
2025-02-11 09:57:58 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:58:41 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:57:57 AM
2025-02-11 09:58:41 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.74, Volume=0
2025-02-11 09:58:41 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:59:04 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:58:40 AM
2025-02-11 09:59:04 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.61, Volume=0
2025-02-11 09:59:04 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:59:13 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:59:03 AM
2025-02-11 09:59:13 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.49, Volume=0
2025-02-11 09:59:13 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 09:59:36 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:59:12 AM
2025-02-11 09:59:36 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.36, Volume=0
2025-02-11 09:59:36 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 10:00:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/11/2025 10:00:00 AM
2025-02-11 10:00:01 [DATA-FLOW] Received market data for NVDA: Time=2/11/2025 10:00:00 AM, Close=133.51, Volume=32549
2025-02-11 10:00:13 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 9:59:35 AM
2025-02-11 10:00:13 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.61, Volume=0
2025-02-11 10:00:13 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 10:00:15 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 10:00:12 AM
2025-02-11 10:00:15 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.74, Volume=0
2025-02-11 10:00:15 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 10:02:21 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 10:00:14 AM
2025-02-11 10:02:21 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.49, Volume=0
2025-02-11 10:02:21 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 10:03:16 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 10:02:20 AM
2025-02-11 10:03:16 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.36, Volume=0
2025-02-11 10:03:16 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 10:04:23 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 10:03:15 AM
2025-02-11 10:04:23 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.23, Volume=0
2025-02-11 10:04:23 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 10:05:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/11/2025 10:05:00 AM
2025-02-11 10:05:34 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 10:04:22 AM
2025-02-11 10:05:34 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.10, Volume=0
2025-02-11 10:05:34 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 10:05:37 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 10:05:33 AM
2025-02-11 10:05:37 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.97, Volume=0
2025-02-11 10:05:37 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 10:07:35 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 10:05:36 AM
2025-02-11 10:07:35 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.23, Volume=0
2025-02-11 10:07:35 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 10:08:21 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 10:07:34 AM
2025-02-11 10:08:21 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.97, Volume=0
2025-02-11 10:08:21 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 10:09:27 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 10:08:20 AM
2025-02-11 10:09:27 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.23, Volume=0
2025-02-11 10:09:27 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 10:09:33 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 10:09:26 AM
2025-02-11 10:09:33 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.36, Volume=0
2025-02-11 10:09:33 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 10:09:58 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 10:09:32 AM
2025-02-11 10:09:58 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.49, Volume=0
2025-02-11 10:09:58 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
2025-02-11 10:10:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/11/2025 10:10:00 AM
2025-02-11 10:12:20 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/11/2025 10:09:57 AM
2025-02-11 10:12:20 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.61, Volume=0
2025-02-11 10:12:20 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (RenkoCompatibleDollarVolumeConsolidator)...
You currently have a maximum of 100kb of log data per backtest, and 100kb total max per day. Have you tried step-by-step debugging? See more at https://qnt.co/debugging or upgrade your organization type for more logs at https://www.quantconnect.com/pricing