2025-02-08 00:00:00 Launching analysis for a406366b12c787135bad456223ce6938 with LEAN Engine v2.5.0.0.17126
2025-02-08 00:00:00 Accurate daily end-times now enabled by default. See more at https://qnt.co/3YHaWHL. To disable it and use legacy daily bars set self.settings.daily_precise_end_time = False.
2025-02-08 00:00:00 Algorithm starting warm up...
2025-02-08 00:00:00 LogManager: Added log window: 2025-02-01 00:00:00 to 2025-05-13 00:00:00
2025-02-08 00:00:00 LogManager Initialized.
2025-02-08 00:00:00 Setting warmup period from 2/8/2025 to 2/13/2025 (5 days)
2025-02-08 00:00:00 [ALPHA-CONFIG] Initialized with insight duration: 200 bars at Daily resolution. Renko Enabled: True
2025-02-08 00:00:00 Trading UNDERLYING NVDA with position size 100%, insight duration: 200 bars at Daily resolution. Renko Volume Aggregation Chunk: 15, <PERSON><PERSON> Price Pct: 0.070%
2025-02-08 00:00:00 [MAX-PCM] Initialized with maximum allocation: 100%, optimize rebalancing: True
2025-02-08 00:00:00 DMI+ATR FlipFlop strategy initialized with custom warmup from beginning of previous year
2025-02-08 00:00:00 Indicator settings: 15-min bars, ADX period 10, threshold 20
2025-02-08 00:00:00 [SECURITIES] 1 securities added to algorithm
2025-02-08 00:00:00 [SECURITIES-CHANGED] OnSecuritiesChanged called with 1 added, 0 removed
2025-02-08 00:00:00 [SECURITIES-CHANGED] Target underlying symbol: NVDA
2025-02-08 00:00:00 [SECURITIES-CHANGED] Processing added security: NVDA (Type: Equity)
2025-02-08 00:00:00 [ALPHA-FILTER] Adding indicators for NVDA (underlying security)
2025-02-08 00:00:00 [ALPHA_MODEL] Initializing Chained Renko for NVDA. Calculating initial first consolidator target value...
2025-02-08 00:00:00 [ALPHA_MODEL] GetPreviousDayMedianDollarValue for NVDA at 2/8/2025 12:00:00 AM
2025-02-08 00:00:00 [ALPHA_MODEL] GetPreviousDayMedianDollarValue for NVDA: Median dollar value: 1043949226.8700 from 208 chunks.
2025-02-08 00:00:00 [ALPHA_MODEL] Initial Renko params for NVDA (DollarInput): DollarValue=1043949226.8700
2025-02-08 00:00:00 [ALPHA_MODEL] GetCurrentPriceBarSize for NVDA at 2/8/2025 12:00:00 AM, Mode: DollarVolumeInput
2025-02-08 00:00:00 [ALPHA_MODEL] GetCurrentPriceBarSize: Mode is AdaptiveRange. Levels: 30, LookbackDays: 10
2025-02-08 00:00:00 [ALPHA_MODEL] GetCurrentPriceBarSize: Fetched 3120 minute bars for NVDA over the last 10 calendar days for AdaptiveRange (ending 2/8/2025 12:00:00 AM).
2025-02-08 00:00:00 [ALPHA_MODEL] GetCurrentPriceBarSize: Calculated average daily delta for NVDA: 5.02625 from 8 valid daily ranges.
2025-02-08 00:00:00 [ALPHA_MODEL] GetCurrentPriceBarSize: AdaptiveRange calculated final bar size for NVDA: 0.16754
2025-02-08 00:00:00 [ALPHA_MODEL] Initial Renko params for NVDA: PriceSize=0.1675416666666666666666666667 (determined by mode: DollarVolumeInput)
2025-02-08 00:00:00 [ALPHA_MODEL] SetupRenkoConsolidator for NVDA: Using RenkoCompatibleDollarVolumeConsolidator with CONSTANT target=$1000 (FIXED: Will always trigger consolidation)
2025-02-08 00:00:00 [CONSOLIDATOR-SETUP] Creating DebugSequentialConsolidator with order: DynamicRenkoConsolidator -> RenkoCompatibleDollarVolumeConsolidator
2025-02-08 00:00:00 [DEBUG-WRAPPER-SECOND] Created wrapper around RenkoCompatibleDollarVolumeConsolidator
2025-02-08 00:00:00 [DEBUG-SEQUENTIAL-INIT] Creating DebugSequentialConsolidator: DynamicRenkoConsolidator -> DebugConsolidatorWrapper
2025-02-08 00:00:00 [DEBUG-SEQUENTIAL-INIT] Input Type: IBaseData, Output Type: VolumeBar
2025-02-08 00:00:00 [DEBUG-SEQUENTIAL-INIT] Event handlers wired up successfully
2025-02-08 00:00:00 [CONSOLIDATOR-SETUP] DebugSequentialConsolidator created successfully. InputType: IBaseData, OutputType: VolumeBar
2025-02-08 00:00:00 [CONSOLIDATOR-SETUP] SUCCESS: DebugSequentialConsolidator chain created for NVDA:
2025-02-08 00:00:00 [CONSOLIDATOR-SETUP]   First (receives raw data): DynamicRenkoConsolidator(0.1675416666666666666666666667)
2025-02-08 00:00:00 [CONSOLIDATOR-SETUP]   Second (wrapped, receives Renko output): DebugConsolidatorWrapper -> RenkoCompatibleDollarVolumeConsolidator(1043949226.8700)
2025-02-08 00:00:00 [CONSOLIDATOR-SETUP]   Data flow: Raw Data -> DynamicRenkoConsolidator -> DebugWrapper(RenkoCompatibleDollarVolumeConsolidator) -> OnRenkoDataConsolidated
2025-02-08 00:00:00 [CONSOLIDATOR-SETUP] WATCH: Look for '[DEBUG-SEQUENTIAL-*]' and '[CONSOLIDATOR-FIX-SUCCESS]' messages to verify the fix is working!
2025-02-08 00:00:00 [ALPHA_MODEL] Scheduled daily Renko consolidator reset and recreation for NVDA at midnight.
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 12:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 12:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 12:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 12:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 12:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 12:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 1:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 1:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 1:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 1:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 1:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 1:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 2:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 2:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 2:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 2:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 2:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 2:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 3:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 3:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 3:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 3:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 3:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 3:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 4:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 4:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 4:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 4:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 4:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 4:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 5:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 5:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 5:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 5:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 5:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 5:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 6:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 6:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 6:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 6:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 6:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 6:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 7:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 7:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 7:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 7:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 7:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 7:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 8:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 8:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 8:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 8:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 8:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 8:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 9:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 9:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 9:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 9:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 9:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 9:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 10:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 10:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 10:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 10:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 10:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 10:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 11:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 11:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 11:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 11:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 11:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 11:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 12:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 12:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 12:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 12:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 12:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 12:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 1:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 1:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 1:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 1:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 1:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 1:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 2:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 2:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 2:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 2:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 2:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 2:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 3:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 3:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 3:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 3:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 3:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 3:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 4:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 4:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 4:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 4:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 4:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 4:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 5:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 5:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 5:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 5:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 5:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 5:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 6:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 6:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 6:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 6:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 6:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 6:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 7:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 7:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 7:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 7:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 7:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 7:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 8:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 8:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 8:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 8:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 8:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 8:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 9:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 9:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 9:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 9:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 9:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 9:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 10:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 10:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 10:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 10:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 10:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 10:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 11:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 11:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 11:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 11:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 11:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/8/2025 11:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 12:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 12:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 12:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 12:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 12:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 12:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 1:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 1:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 1:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 1:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 1:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 1:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 2:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 2:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 2:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 2:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 2:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 2:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 3:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 3:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 3:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 3:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 3:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 3:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 4:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 4:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 4:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 4:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 4:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 4:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 5:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 5:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 5:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 5:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 5:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 5:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 6:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 6:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 6:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 6:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 6:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 6:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 7:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 7:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 7:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 7:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 7:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 7:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 8:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 8:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 8:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 8:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 8:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 8:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 9:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 9:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 9:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 9:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 9:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 9:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 10:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 10:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 10:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 10:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 10:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 10:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 11:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 11:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 11:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 11:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 11:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 11:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 12:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 12:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 12:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 12:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 12:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 12:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 1:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 1:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 1:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 1:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 1:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 1:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 2:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 2:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 2:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 2:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 2:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 2:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 3:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 3:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 3:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 3:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 3:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 3:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 4:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 4:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 4:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 4:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 4:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 4:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 5:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 5:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 5:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 5:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 5:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 5:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 6:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 6:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 6:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 6:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 6:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 6:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 7:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 7:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 7:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 7:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 7:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 7:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 8:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 8:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 8:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 8:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 8:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 8:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 9:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 9:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 9:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 9:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 9:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 9:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 10:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 10:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 10:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 10:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 10:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 10:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 11:00:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 11:10:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 11:20:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 11:30:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 11:40:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/9/2025 11:50:01 PM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 12:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 12:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 12:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 12:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 12:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 12:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 1:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 1:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 1:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 1:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 1:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 1:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 2:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 2:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 2:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 2:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 2:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 2:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 3:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 3:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 3:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 3:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 3:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 3:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 4:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 4:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 4:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 4:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 4:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 4:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 5:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 5:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 5:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 5:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 5:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 5:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 6:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 6:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 6:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 6:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 6:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 6:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 7:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 7:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 7:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 7:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 7:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 7:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 8:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 8:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 8:20:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 8:30:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 8:40:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 8:50:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 9:00:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 9:10:01 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 9:20:01 AM
2025-02-10 09:30:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 9:30:00 AM
2025-02-10 09:30:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 9:30:01 AM
2025-02-10 09:30:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 9:30:00 AM, Close=130.02, Volume=2666961
2025-02-10 09:30:06 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:30:00 AM
2025-02-10 09:30:06 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=130.18, Volume=0
2025-02-10 09:30:06 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:30:06 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:30:06 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:30:00 AM
2025-02-10 09:30:06 [DEBUG-WRAPPER-SECOND] Data details: Close=130.18, Volume=0
2025-02-10 09:30:06 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:30:06 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:30:06 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:30:06 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:30:06 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:30:00 AM
2025-02-10 09:30:08 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:30:05 AM
2025-02-10 09:30:08 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=130.35, Volume=0
2025-02-10 09:30:08 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:30:08 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:30:08 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:30:05 AM
2025-02-10 09:30:08 [DEBUG-WRAPPER-SECOND] Data details: Close=130.35, Volume=0
2025-02-10 09:30:08 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:30:08 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:30:08 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:30:08 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:30:08 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:30:05 AM
2025-02-10 09:30:35 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:30:07 AM
2025-02-10 09:30:35 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=130.51, Volume=0
2025-02-10 09:30:35 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:30:35 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:30:35 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:30:07 AM
2025-02-10 09:30:35 [DEBUG-WRAPPER-SECOND] Data details: Close=130.51, Volume=0
2025-02-10 09:30:35 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:30:35 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:30:35 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:30:35 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:30:35 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:30:07 AM
2025-02-10 09:30:47 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:30:34 AM
2025-02-10 09:30:47 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=130.68, Volume=0
2025-02-10 09:30:47 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:30:47 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:30:47 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:30:34 AM
2025-02-10 09:30:47 [DEBUG-WRAPPER-SECOND] Data details: Close=130.68, Volume=0
2025-02-10 09:30:47 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:30:47 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:30:47 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:30:47 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:30:47 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:30:34 AM
2025-02-10 09:30:55 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:30:46 AM
2025-02-10 09:30:55 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=130.85, Volume=0
2025-02-10 09:30:55 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:30:55 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:30:55 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:30:46 AM
2025-02-10 09:30:55 [DEBUG-WRAPPER-SECOND] Data details: Close=130.85, Volume=0
2025-02-10 09:30:55 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:30:55 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:30:55 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:30:55 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:30:55 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:30:46 AM
2025-02-10 09:31:13 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:30:54 AM
2025-02-10 09:31:13 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.02, Volume=0
2025-02-10 09:31:13 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:31:13 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:31:13 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:30:54 AM
2025-02-10 09:31:13 [DEBUG-WRAPPER-SECOND] Data details: Close=131.02, Volume=0
2025-02-10 09:31:13 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:31:13 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:31:13 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:31:13 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:31:13 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:30:54 AM
2025-02-10 09:31:15 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:31:12 AM
2025-02-10 09:31:15 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.19, Volume=0
2025-02-10 09:31:15 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:31:15 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:31:15 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:31:12 AM
2025-02-10 09:31:15 [DEBUG-WRAPPER-SECOND] Data details: Close=131.19, Volume=0
2025-02-10 09:31:15 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:31:15 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:31:15 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:31:15 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:31:15 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:31:12 AM
2025-02-10 09:31:32 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:31:14 AM
2025-02-10 09:31:32 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.35, Volume=0
2025-02-10 09:31:32 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:31:32 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:31:32 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:31:14 AM
2025-02-10 09:31:32 [DEBUG-WRAPPER-SECOND] Data details: Close=131.35, Volume=0
2025-02-10 09:31:32 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:31:32 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:31:32 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:31:32 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:31:32 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:31:14 AM
2025-02-10 09:31:39 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:31:31 AM
2025-02-10 09:31:39 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.52, Volume=0
2025-02-10 09:31:39 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:31:39 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:31:39 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:31:31 AM
2025-02-10 09:31:39 [DEBUG-WRAPPER-SECOND] Data details: Close=131.52, Volume=0
2025-02-10 09:31:39 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:31:39 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:31:39 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:31:39 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:31:39 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:31:31 AM
2025-02-10 09:31:59 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:31:38 AM
2025-02-10 09:31:59 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.69, Volume=0
2025-02-10 09:31:59 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:31:59 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:31:59 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:31:38 AM
2025-02-10 09:31:59 [DEBUG-WRAPPER-SECOND] Data details: Close=131.69, Volume=0
2025-02-10 09:31:59 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:31:59 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:31:59 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:31:59 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:31:59 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:31:38 AM
2025-02-10 09:33:09 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:31:58 AM
2025-02-10 09:33:09 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.35, Volume=0
2025-02-10 09:33:09 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:33:09 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:33:09 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:31:58 AM
2025-02-10 09:33:09 [DEBUG-WRAPPER-SECOND] Data details: Close=131.35, Volume=0
2025-02-10 09:33:09 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:33:09 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:33:09 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:33:09 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:33:09 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:31:58 AM
2025-02-10 09:33:25 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:33:08 AM
2025-02-10 09:33:25 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.69, Volume=0
2025-02-10 09:33:25 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:33:25 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:33:25 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:33:08 AM
2025-02-10 09:33:25 [DEBUG-WRAPPER-SECOND] Data details: Close=131.69, Volume=0
2025-02-10 09:33:25 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:33:25 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:33:25 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:33:25 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:33:25 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:33:08 AM
2025-02-10 09:33:44 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:33:24 AM
2025-02-10 09:33:44 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.35, Volume=0
2025-02-10 09:33:44 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:33:44 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:33:44 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:33:24 AM
2025-02-10 09:33:44 [DEBUG-WRAPPER-SECOND] Data details: Close=131.35, Volume=0
2025-02-10 09:33:44 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:33:44 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:33:44 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:33:44 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:33:44 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:33:24 AM
2025-02-10 09:34:05 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:33:43 AM
2025-02-10 09:34:05 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.69, Volume=0
2025-02-10 09:34:05 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:34:05 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:34:05 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:33:43 AM
2025-02-10 09:34:05 [DEBUG-WRAPPER-SECOND] Data details: Close=131.69, Volume=0
2025-02-10 09:34:05 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:34:05 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:34:05 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:34:05 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:34:05 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:33:43 AM
2025-02-10 09:34:38 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:34:04 AM
2025-02-10 09:34:38 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=131.86, Volume=0
2025-02-10 09:34:38 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:34:38 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:34:38 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:34:04 AM
2025-02-10 09:34:38 [DEBUG-WRAPPER-SECOND] Data details: Close=131.86, Volume=0
2025-02-10 09:34:38 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:34:38 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:34:38 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:34:38 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:34:38 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:34:04 AM
2025-02-10 09:35:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 9:35:00 AM
2025-02-10 09:35:18 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:34:37 AM
2025-02-10 09:35:18 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.02, Volume=0
2025-02-10 09:35:18 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:35:18 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:35:18 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:34:37 AM
2025-02-10 09:35:18 [DEBUG-WRAPPER-SECOND] Data details: Close=132.02, Volume=0
2025-02-10 09:35:18 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:35:18 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:35:18 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:35:18 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:35:18 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:34:37 AM
2025-02-10 09:35:26 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:35:17 AM
2025-02-10 09:35:26 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.19, Volume=0
2025-02-10 09:35:26 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:35:26 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:35:26 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:35:17 AM
2025-02-10 09:35:26 [DEBUG-WRAPPER-SECOND] Data details: Close=132.19, Volume=0
2025-02-10 09:35:26 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:35:26 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:35:26 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:35:26 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:35:26 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:35:17 AM
2025-02-10 09:35:45 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:35:25 AM
2025-02-10 09:35:45 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.36, Volume=0
2025-02-10 09:35:45 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:35:45 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:35:45 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:35:25 AM
2025-02-10 09:35:45 [DEBUG-WRAPPER-SECOND] Data details: Close=132.36, Volume=0
2025-02-10 09:35:45 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:35:45 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:35:45 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:35:45 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:35:45 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:35:25 AM
2025-02-10 09:36:11 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:35:44 AM
2025-02-10 09:36:11 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.53, Volume=0
2025-02-10 09:36:11 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:36:11 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:36:11 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:35:44 AM
2025-02-10 09:36:11 [DEBUG-WRAPPER-SECOND] Data details: Close=132.53, Volume=0
2025-02-10 09:36:11 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:36:11 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:36:11 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:36:11 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:36:11 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:35:44 AM
2025-02-10 09:36:19 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:36:10 AM
2025-02-10 09:36:19 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.69, Volume=0
2025-02-10 09:36:19 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:36:19 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:36:19 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:36:10 AM
2025-02-10 09:36:19 [DEBUG-WRAPPER-SECOND] Data details: Close=132.69, Volume=0
2025-02-10 09:36:19 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:36:19 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:36:19 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:36:19 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:36:19 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:36:10 AM
2025-02-10 09:37:04 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:36:18 AM
2025-02-10 09:37:04 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.86, Volume=0
2025-02-10 09:37:04 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:37:04 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:37:04 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:36:18 AM
2025-02-10 09:37:04 [DEBUG-WRAPPER-SECOND] Data details: Close=132.86, Volume=0
2025-02-10 09:37:04 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:37:04 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:37:04 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:37:04 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:37:04 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:36:18 AM
2025-02-10 09:37:22 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:37:03 AM
2025-02-10 09:37:22 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.03, Volume=0
2025-02-10 09:37:22 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:37:22 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:37:22 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:37:03 AM
2025-02-10 09:37:22 [DEBUG-WRAPPER-SECOND] Data details: Close=133.03, Volume=0
2025-02-10 09:37:22 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:37:22 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:37:22 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:37:22 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:37:22 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:37:03 AM
2025-02-10 09:38:07 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:37:21 AM
2025-02-10 09:38:07 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.20, Volume=0
2025-02-10 09:38:07 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:38:07 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:38:07 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:37:21 AM
2025-02-10 09:38:07 [DEBUG-WRAPPER-SECOND] Data details: Close=133.20, Volume=0
2025-02-10 09:38:07 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:38:07 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:38:07 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:38:07 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:38:07 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:37:21 AM
2025-02-10 09:38:22 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:38:06 AM
2025-02-10 09:38:22 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.36, Volume=0
2025-02-10 09:38:22 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:38:22 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:38:22 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:38:06 AM
2025-02-10 09:38:22 [DEBUG-WRAPPER-SECOND] Data details: Close=133.36, Volume=0
2025-02-10 09:38:22 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:38:22 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:38:22 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:38:22 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:38:22 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:38:06 AM
2025-02-10 09:38:35 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:38:21 AM
2025-02-10 09:38:35 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.53, Volume=0
2025-02-10 09:38:35 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:38:35 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:38:35 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:38:21 AM
2025-02-10 09:38:35 [DEBUG-WRAPPER-SECOND] Data details: Close=133.53, Volume=0
2025-02-10 09:38:35 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:38:35 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:38:35 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:38:35 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:38:35 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:38:21 AM
2025-02-10 09:39:19 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:38:34 AM
2025-02-10 09:39:19 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.70, Volume=0
2025-02-10 09:39:19 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:39:19 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:39:19 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:38:34 AM
2025-02-10 09:39:19 [DEBUG-WRAPPER-SECOND] Data details: Close=133.70, Volume=0
2025-02-10 09:39:19 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:39:19 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:39:19 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:39:19 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:39:19 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:38:34 AM
2025-02-10 09:39:40 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:39:18 AM
2025-02-10 09:39:40 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.87, Volume=0
2025-02-10 09:39:40 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:39:40 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:39:40 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:39:18 AM
2025-02-10 09:39:40 [DEBUG-WRAPPER-SECOND] Data details: Close=133.87, Volume=0
2025-02-10 09:39:40 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:39:40 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:39:40 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:39:40 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:39:40 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:39:18 AM
2025-02-10 09:40:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 9:40:00 AM
2025-02-10 09:40:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 9:40:01 AM
2025-02-10 09:40:17 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:39:39 AM
2025-02-10 09:40:17 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.53, Volume=0
2025-02-10 09:40:17 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:40:17 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:40:17 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:39:39 AM
2025-02-10 09:40:17 [DEBUG-WRAPPER-SECOND] Data details: Close=133.53, Volume=0
2025-02-10 09:40:17 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:40:17 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:40:17 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:40:17 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:40:17 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:39:39 AM
2025-02-10 09:40:27 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:40:16 AM
2025-02-10 09:40:27 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.36, Volume=0
2025-02-10 09:40:27 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:40:27 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:40:27 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:40:16 AM
2025-02-10 09:40:27 [DEBUG-WRAPPER-SECOND] Data details: Close=133.36, Volume=0
2025-02-10 09:40:27 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:40:27 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:40:27 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:40:27 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:40:27 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:40:16 AM
2025-02-10 09:40:39 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:40:26 AM
2025-02-10 09:40:39 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.20, Volume=0
2025-02-10 09:40:39 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:40:39 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:40:39 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:40:26 AM
2025-02-10 09:40:39 [DEBUG-WRAPPER-SECOND] Data details: Close=133.20, Volume=0
2025-02-10 09:40:39 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:40:39 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:40:39 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:40:39 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:40:39 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:40:26 AM
2025-02-10 09:41:26 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:40:38 AM
2025-02-10 09:41:26 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.03, Volume=0
2025-02-10 09:41:26 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:41:26 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:41:26 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:40:38 AM
2025-02-10 09:41:26 [DEBUG-WRAPPER-SECOND] Data details: Close=133.03, Volume=0
2025-02-10 09:41:26 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:41:26 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:41:26 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:41:26 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:41:26 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:40:38 AM
2025-02-10 09:41:40 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:41:25 AM
2025-02-10 09:41:40 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=132.86, Volume=0
2025-02-10 09:41:40 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:41:40 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:41:40 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:41:25 AM
2025-02-10 09:41:40 [DEBUG-WRAPPER-SECOND] Data details: Close=132.86, Volume=0
2025-02-10 09:41:40 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:41:40 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:41:40 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:41:40 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:41:40 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:41:25 AM
2025-02-10 09:42:21 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:41:39 AM
2025-02-10 09:42:21 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.20, Volume=0
2025-02-10 09:42:21 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:42:21 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:42:21 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:41:39 AM
2025-02-10 09:42:21 [DEBUG-WRAPPER-SECOND] Data details: Close=133.20, Volume=0
2025-02-10 09:42:21 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:42:21 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:42:21 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:42:21 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:42:21 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:41:39 AM
2025-02-10 09:42:29 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:42:20 AM
2025-02-10 09:42:29 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.36, Volume=0
2025-02-10 09:42:29 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:42:29 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:42:29 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:42:20 AM
2025-02-10 09:42:29 [DEBUG-WRAPPER-SECOND] Data details: Close=133.36, Volume=0
2025-02-10 09:42:29 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:42:29 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:42:29 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:42:29 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:42:29 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:42:20 AM
2025-02-10 09:42:58 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:42:28 AM
2025-02-10 09:42:58 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.53, Volume=0
2025-02-10 09:42:58 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:42:58 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:42:58 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:42:28 AM
2025-02-10 09:42:58 [DEBUG-WRAPPER-SECOND] Data details: Close=133.53, Volume=0
2025-02-10 09:42:58 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:42:58 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:42:58 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:42:58 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:42:58 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:42:28 AM
2025-02-10 09:45:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 9:45:00 AM
2025-02-10 09:45:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 9:45:00 AM, Close=133.40, Volume=17678
2025-02-10 09:45:27 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:42:57 AM
2025-02-10 09:45:27 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.70, Volume=0
2025-02-10 09:45:27 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:45:27 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:45:27 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:42:57 AM
2025-02-10 09:45:27 [DEBUG-WRAPPER-SECOND] Data details: Close=133.70, Volume=0
2025-02-10 09:45:27 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:45:27 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:45:27 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:45:27 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:45:27 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:42:57 AM
2025-02-10 09:46:10 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:45:26 AM
2025-02-10 09:46:10 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.87, Volume=0
2025-02-10 09:46:10 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:46:10 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:46:10 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:45:26 AM
2025-02-10 09:46:10 [DEBUG-WRAPPER-SECOND] Data details: Close=133.87, Volume=0
2025-02-10 09:46:10 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:46:10 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:46:10 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:46:10 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:46:10 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:45:26 AM
2025-02-10 09:46:33 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:46:09 AM
2025-02-10 09:46:33 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.03, Volume=0
2025-02-10 09:46:33 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:46:33 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:46:33 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:46:09 AM
2025-02-10 09:46:33 [DEBUG-WRAPPER-SECOND] Data details: Close=134.03, Volume=0
2025-02-10 09:46:33 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:46:33 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:46:33 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:46:33 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:46:33 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:46:09 AM
2025-02-10 09:46:49 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:46:32 AM
2025-02-10 09:46:49 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.20, Volume=0
2025-02-10 09:46:49 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:46:49 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:46:49 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:46:32 AM
2025-02-10 09:46:49 [DEBUG-WRAPPER-SECOND] Data details: Close=134.20, Volume=0
2025-02-10 09:46:49 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:46:49 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:46:49 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:46:49 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:46:49 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:46:32 AM
2025-02-10 09:47:07 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:46:48 AM
2025-02-10 09:47:07 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.37, Volume=0
2025-02-10 09:47:07 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:47:07 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:47:07 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:46:48 AM
2025-02-10 09:47:07 [DEBUG-WRAPPER-SECOND] Data details: Close=134.37, Volume=0
2025-02-10 09:47:07 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:47:07 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:47:07 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:47:07 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:47:07 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:46:48 AM
2025-02-10 09:49:49 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:47:06 AM
2025-02-10 09:49:49 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.03, Volume=0
2025-02-10 09:49:49 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:49:49 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:49:49 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:47:06 AM
2025-02-10 09:49:49 [DEBUG-WRAPPER-SECOND] Data details: Close=134.03, Volume=0
2025-02-10 09:49:49 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:49:49 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:49:49 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:49:49 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:49:49 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:47:06 AM
2025-02-10 09:50:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 9:50:00 AM
2025-02-10 09:50:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 9:50:01 AM
2025-02-10 09:50:04 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:49:48 AM
2025-02-10 09:50:04 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.87, Volume=0
2025-02-10 09:50:04 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:50:04 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:50:04 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:49:48 AM
2025-02-10 09:50:04 [DEBUG-WRAPPER-SECOND] Data details: Close=133.87, Volume=0
2025-02-10 09:50:04 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:50:04 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:50:04 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:50:04 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:50:04 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:49:48 AM
2025-02-10 09:50:16 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:50:03 AM
2025-02-10 09:50:16 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.70, Volume=0
2025-02-10 09:50:16 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:50:16 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:50:16 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:50:03 AM
2025-02-10 09:50:16 [DEBUG-WRAPPER-SECOND] Data details: Close=133.70, Volume=0
2025-02-10 09:50:16 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:50:16 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:50:16 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:50:16 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:50:16 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:50:03 AM
2025-02-10 09:50:45 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:50:15 AM
2025-02-10 09:50:45 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.03, Volume=0
2025-02-10 09:50:45 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:50:45 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:50:45 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:50:15 AM
2025-02-10 09:50:45 [DEBUG-WRAPPER-SECOND] Data details: Close=134.03, Volume=0
2025-02-10 09:50:45 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:50:45 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:50:45 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:50:45 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:50:45 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:50:15 AM
2025-02-10 09:50:53 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:50:44 AM
2025-02-10 09:50:53 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.20, Volume=0
2025-02-10 09:50:53 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:50:53 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:50:53 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:50:44 AM
2025-02-10 09:50:53 [DEBUG-WRAPPER-SECOND] Data details: Close=134.20, Volume=0
2025-02-10 09:50:53 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:50:53 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:50:53 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:50:53 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:50:53 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:50:44 AM
2025-02-10 09:51:44 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:50:52 AM
2025-02-10 09:51:44 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.87, Volume=0
2025-02-10 09:51:44 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:51:44 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:51:44 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:50:52 AM
2025-02-10 09:51:44 [DEBUG-WRAPPER-SECOND] Data details: Close=133.87, Volume=0
2025-02-10 09:51:44 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:51:44 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:51:44 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:51:44 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:51:44 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:50:52 AM
2025-02-10 09:52:29 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:51:43 AM
2025-02-10 09:52:29 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.70, Volume=0
2025-02-10 09:52:29 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:52:29 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:52:29 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:51:43 AM
2025-02-10 09:52:29 [DEBUG-WRAPPER-SECOND] Data details: Close=133.70, Volume=0
2025-02-10 09:52:29 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:52:29 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:52:29 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:52:29 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:52:29 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:51:43 AM
2025-02-10 09:53:16 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:52:28 AM
2025-02-10 09:53:16 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.03, Volume=0
2025-02-10 09:53:16 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:53:16 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:53:16 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:52:28 AM
2025-02-10 09:53:16 [DEBUG-WRAPPER-SECOND] Data details: Close=134.03, Volume=0
2025-02-10 09:53:16 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:53:16 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:53:16 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:53:16 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:53:16 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:52:28 AM
2025-02-10 09:54:25 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:53:15 AM
2025-02-10 09:54:25 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.20, Volume=0
2025-02-10 09:54:25 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:54:25 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:54:25 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:53:15 AM
2025-02-10 09:54:25 [DEBUG-WRAPPER-SECOND] Data details: Close=134.20, Volume=0
2025-02-10 09:54:25 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:54:25 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:54:25 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:54:25 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:54:25 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:53:15 AM
2025-02-10 09:55:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 9:55:00 AM
2025-02-10 09:57:18 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:54:24 AM
2025-02-10 09:57:18 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.87, Volume=0
2025-02-10 09:57:18 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:57:18 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:57:18 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:54:24 AM
2025-02-10 09:57:18 [DEBUG-WRAPPER-SECOND] Data details: Close=133.87, Volume=0
2025-02-10 09:57:18 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:57:18 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:57:18 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:57:18 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:57:18 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:54:24 AM
2025-02-10 09:58:37 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:57:17 AM
2025-02-10 09:58:37 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.70, Volume=0
2025-02-10 09:58:37 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 09:58:37 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 09:58:37 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:57:17 AM
2025-02-10 09:58:37 [DEBUG-WRAPPER-SECOND] Data details: Close=133.70, Volume=0
2025-02-10 09:58:37 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 09:58:37 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 09:58:37 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 09:58:37 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 09:58:37 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:57:17 AM
2025-02-10 10:00:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:00:00 AM
2025-02-10 10:00:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 10:00:01 AM
2025-02-10 10:00:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 10:00:00 AM, Close=133.81, Volume=23771
2025-02-10 10:01:27 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 9:58:36 AM
2025-02-10 10:01:27 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.03, Volume=0
2025-02-10 10:01:27 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 10:01:27 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 10:01:27 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 9:58:36 AM
2025-02-10 10:01:27 [DEBUG-WRAPPER-SECOND] Data details: Close=134.03, Volume=0
2025-02-10 10:01:27 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 10:01:27 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 10:01:27 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 10:01:27 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 10:01:27 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 9:58:36 AM
2025-02-10 10:01:31 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:01:26 AM
2025-02-10 10:01:31 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.20, Volume=0
2025-02-10 10:01:31 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 10:01:31 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 10:01:31 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 10:01:26 AM
2025-02-10 10:01:31 [DEBUG-WRAPPER-SECOND] Data details: Close=134.20, Volume=0
2025-02-10 10:01:31 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 10:01:31 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 10:01:31 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 10:01:31 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 10:01:31 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 10:01:26 AM
2025-02-10 10:02:15 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:01:30 AM
2025-02-10 10:02:15 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.87, Volume=0
2025-02-10 10:02:15 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 10:02:15 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 10:02:15 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 10:01:30 AM
2025-02-10 10:02:15 [DEBUG-WRAPPER-SECOND] Data details: Close=133.87, Volume=0
2025-02-10 10:02:15 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 10:02:15 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 10:02:15 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 10:02:15 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 10:02:15 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 10:01:30 AM
2025-02-10 10:02:33 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:02:14 AM
2025-02-10 10:02:33 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.70, Volume=0
2025-02-10 10:02:33 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 10:02:33 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 10:02:33 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 10:02:14 AM
2025-02-10 10:02:33 [DEBUG-WRAPPER-SECOND] Data details: Close=133.70, Volume=0
2025-02-10 10:02:33 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 10:02:33 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 10:02:33 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 10:02:33 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 10:02:33 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 10:02:14 AM
2025-02-10 10:03:04 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:02:32 AM
2025-02-10 10:03:04 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.53, Volume=0
2025-02-10 10:03:04 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 10:03:04 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 10:03:04 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 10:02:32 AM
2025-02-10 10:03:04 [DEBUG-WRAPPER-SECOND] Data details: Close=133.53, Volume=0
2025-02-10 10:03:04 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 10:03:04 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 10:03:04 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 10:03:04 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 10:03:04 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 10:02:32 AM
2025-02-10 10:03:40 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:03:03 AM
2025-02-10 10:03:40 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=133.87, Volume=0
2025-02-10 10:03:40 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 10:03:40 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 10:03:40 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 10:03:03 AM
2025-02-10 10:03:40 [DEBUG-WRAPPER-SECOND] Data details: Close=133.87, Volume=0
2025-02-10 10:03:40 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 10:03:40 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 10:03:40 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 10:03:40 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 10:03:40 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 10:03:03 AM
2025-02-10 10:05:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:05:00 AM
2025-02-10 10:08:30 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:03:39 AM
2025-02-10 10:08:30 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.03, Volume=0
2025-02-10 10:08:30 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 10:08:30 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 10:08:30 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 10:03:39 AM
2025-02-10 10:08:30 [DEBUG-WRAPPER-SECOND] Data details: Close=134.03, Volume=0
2025-02-10 10:08:30 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 10:08:30 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 10:08:30 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 10:08:30 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 10:08:30 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 10:03:39 AM
2025-02-10 10:08:39 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:08:29 AM
2025-02-10 10:08:39 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.20, Volume=0
2025-02-10 10:08:39 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 10:08:39 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 10:08:39 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 10:08:29 AM
2025-02-10 10:08:39 [DEBUG-WRAPPER-SECOND] Data details: Close=134.20, Volume=0
2025-02-10 10:08:39 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 10:08:39 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 10:08:39 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 10:08:39 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 10:08:39 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 10:08:29 AM
2025-02-10 10:10:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:10:00 AM
2025-02-10 10:10:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 10:10:01 AM
2025-02-10 10:13:18 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:08:38 AM
2025-02-10 10:13:18 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.37, Volume=0
2025-02-10 10:13:18 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 10:13:18 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 10:13:18 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 10:08:38 AM
2025-02-10 10:13:18 [DEBUG-WRAPPER-SECOND] Data details: Close=134.37, Volume=0
2025-02-10 10:13:18 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 10:13:18 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 10:13:18 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 10:13:18 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 10:13:18 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 10:08:38 AM
2025-02-10 10:15:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:15:00 AM
2025-02-10 10:15:01 [DATA-FLOW] Received market data for NVDA: Time=2/10/2025 10:15:00 AM, Close=134.33, Volume=6005
2025-02-10 10:16:54 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:13:17 AM
2025-02-10 10:16:54 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.03, Volume=0
2025-02-10 10:16:54 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 10:16:54 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 10:16:54 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 10:13:17 AM
2025-02-10 10:16:54 [DEBUG-WRAPPER-SECOND] Data details: Close=134.03, Volume=0
2025-02-10 10:16:54 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 10:16:54 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 10:16:54 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 10:16:54 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 10:16:54 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 10:13:17 AM
2025-02-10 10:19:06 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:16:53 AM
2025-02-10 10:19:06 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.37, Volume=0
2025-02-10 10:19:06 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 10:19:06 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 10:19:06 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 10:16:53 AM
2025-02-10 10:19:06 [DEBUG-WRAPPER-SECOND] Data details: Close=134.37, Volume=0
2025-02-10 10:19:06 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 10:19:06 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 10:19:06 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 10:19:06 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 10:19:06 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 10:16:53 AM
2025-02-10 10:19:42 [DEBUG-SEQUENTIAL-FIRST] First consolidator (DynamicRenkoConsolidator) produced data: RenkoBar for NVDA at 2/10/2025 10:19:05 AM
2025-02-10 10:19:42 [DEBUG-SEQUENTIAL-FIRST] First output details: Close=134.54, Volume=0
2025-02-10 10:19:42 [DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator (DebugConsolidatorWrapper)...
2025-02-10 10:19:42 [DEBUG-SECOND-INPUT] About to call _second.Update() with RenkoBar
2025-02-10 10:19:42 [DEBUG-WRAPPER-SECOND] Update() called with RenkoBar for NVDA at 2/10/2025 10:19:05 AM
2025-02-10 10:19:42 [DEBUG-WRAPPER-SECOND] Data details: Close=134.54, Volume=0
2025-02-10 10:19:42 [DEBUG-WRAPPER-SECOND] Update() completed
2025-02-10 10:19:42 [DEBUG-SECOND-INPUT] Completed _second.Update() call
2025-02-10 10:19:42 [DEBUG-FIRST-OUTPUT] First consolidator (DynamicRenkoConsolidator) fired DataConsolidated event
2025-02-10 10:19:42 [DEBUG-FIRST-OUTPUT] Sender type: DynamicRenkoConsolidator
2025-02-10 10:19:42 [DEBUG-FIRST-OUTPUT] Data type: RenkoBar, Symbol: NVDA, Time: 2/10/2025 10:19:05 AM
2025-02-10 10:20:01 [DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with TradeBar for NVDA at 2/10/2025 10:20:00 AM
2025-02-10 10:20:01 [DEBUG-WRAPPER-SECOND] Scan() called with time 2/10/2025 10:20:01 AM
2025-02-10 10:20:09 Processing algorithm warm-up request 48%...
You currently have a maximum of 100kb of log data per backtest, and 100kb total max per day. Have you tried step-by-step debugging? See more at https://qnt.co/debugging or upgrade your organization type for more logs at https://www.quantconnect.com/pricing