/*
 * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
 * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
*/

using System;
using QuantConnect.Data.Market;

namespace QuantConnect.Data.Consolidators
{
    /// <summary>
    /// This consolidator extends RenkoConsolidator to properly accumulate and preserve volume information
    /// in the output RenkoBar objects. The original RenkoConsolidator creates RenkoBar objects with zero volume,
    /// which breaks downstream consolidators that depend on volume data.
    /// </summary>
    public class VolumeAwareRenkoConsolidator : IDataConsolidator
    {
        private bool _firstTick = true;
        private RenkoBar _lastWicko;
        private DataConsolidatedHandler _dataConsolidatedHandler;
        private RenkoBar _currentBar;
        private IBaseData _consolidated;
        
        // Volume accumulation fields
        private decimal _accumulatedVolume;
        
        /// <summary>
        /// Time of consolidated close.
        /// </summary>
        /// <remarks>Protected for testing</remarks>
        protected DateTime CloseOn { get; set; }

        /// <summary>
        /// Value of consolidated close.
        /// </summary>
        /// <remarks>Protected for testing</remarks>
        protected decimal CloseRate { get; set; }

        /// <summary>
        /// Value of consolidated high.
        /// </summary>
        /// <remarks>Protected for testing</remarks>
        protected decimal HighRate { get; set; }

        /// <summary>
        /// Value of consolidated low.
        /// </summary>
        /// <remarks>Protected for testing</remarks>
        protected decimal LowRate { get; set; }

        /// <summary>
        /// Time of consolidated open.
        /// </summary>
        /// <remarks>Protected for testing</remarks>
        protected DateTime OpenOn { get; set; }

        /// <summary>
        /// Value of consolidate open.
        /// </summary>
        /// <remarks>Protected for testing</remarks>
        protected decimal OpenRate { get; set; }

        /// <summary>
        /// Size of the consolidated bar.
        /// </summary>
        /// <remarks>Protected for testing</remarks>
        protected decimal BarSize { get; set; }

        /// <summary>
        /// Gets the kind of the bar
        /// </summary>
        public RenkoType Type => RenkoType.Wicked;

        /// <summary>
        /// Gets a clone of the data being currently consolidated
        /// </summary>
        public IBaseData WorkingData => _currentBar?.Clone();

        /// <summary>
        /// Gets the type consumed by this consolidator
        /// </summary>
        public Type InputType => typeof(IBaseData);

        /// <summary>
        /// Gets <see cref="RenkoBar"/> which is the type emitted in the <see cref="IDataConsolidator.DataConsolidated"/> event.
        /// </summary>
        public Type OutputType => typeof(RenkoBar);

        /// <summary>
        /// Gets the most recently consolidated piece of data. This will be null if this consolidator
        /// has not produced any data yet.
        /// </summary>
        public IBaseData Consolidated
        {
            get { return _consolidated; }
            private set { _consolidated = value; }
        }

        /// <summary>
        /// Event handler that fires when a new piece of data is produced
        /// </summary>
        public event EventHandler<RenkoBar> DataConsolidated;

        /// <summary>
        /// Event handler that fires when a new piece of data is produced
        /// </summary>
        event DataConsolidatedHandler IDataConsolidator.DataConsolidated
        {
            add { _dataConsolidatedHandler += value; }
            remove { _dataConsolidatedHandler -= value; }
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="VolumeAwareRenkoConsolidator"/> class using the specified <paramref name="barSize"/>.
        /// </summary>
        /// <param name="barSize">The constant value size of each bar</param>
        public VolumeAwareRenkoConsolidator(decimal barSize)
        {
            if (barSize <= 0)
            {
                throw new ArgumentException("Renko consolidator BarSize must be strictly greater than zero");
            }

            BarSize = barSize;
        }

        /// <summary>
        /// Helper method to extract volume from various data types
        /// </summary>
        private decimal GetVolume(IBaseData data)
        {
            return data switch
            {
                TradeBar tradeBar => tradeBar.Volume,
                QuoteBar quoteBar => quoteBar.LastBidSize + quoteBar.LastAskSize,
                Tick tick => tick.Quantity,
                _ => 0m
            };
        }

        /// <summary>
        /// Updates this consolidator with the specified data
        /// </summary>
        /// <param name="data">The new data for the consolidator</param>
        public void Update(IBaseData data)
        {
            var rate = data.Price;
            var volume = GetVolume(data);

            // Accumulate volume for the current bar
            _accumulatedVolume += volume;

            if (_firstTick)
            {
                _firstTick = false;

                // Round our first rate to the same length as BarSize
                rate = GetClosestMultiple(rate, BarSize);

                OpenOn = data.Time;
                CloseOn = data.Time;
                OpenRate = rate;
                HighRate = rate;
                LowRate = rate;
                CloseRate = rate;
            }
            else
            {
                CloseOn = data.Time;

                if (rate > HighRate)
                {
                    HighRate = rate;
                }

                if (rate < LowRate)
                {
                    LowRate = rate;
                }

                CloseRate = rate;

                if (CloseRate > OpenRate)
                {
                    if (_lastWicko == null || _lastWicko.Direction == BarDirection.Rising)
                    {
                        Rising(data, volume);
                        return;
                    }

                    var limit = _lastWicko.Open + BarSize;

                    if (CloseRate > limit)
                    {
                        var wicko = new RenkoBar(data.Symbol, OpenOn, CloseOn, BarSize, _lastWicko.Open, limit,
                            LowRate, limit);
                        wicko.Volume = _accumulatedVolume;

                        _lastWicko = wicko;

                        OnDataConsolidated(wicko);

                        OpenOn = CloseOn;
                        OpenRate = limit;
                        LowRate = limit;

                        // Reset volume accumulation for next bar
                        _accumulatedVolume = volume; // Keep current volume for potential next bars

                        Rising(data, volume);
                    }
                }
                else if (CloseRate < OpenRate)
                {
                    if (_lastWicko == null || _lastWicko.Direction == BarDirection.Falling)
                    {
                        Falling(data, volume);
                        return;
                    }

                    var limit = _lastWicko.Open - BarSize;

                    if (CloseRate < limit)
                    {
                        var wicko = new RenkoBar(data.Symbol, OpenOn, CloseOn, BarSize, _lastWicko.Open, HighRate,
                            limit, limit);
                        wicko.Volume = _accumulatedVolume;

                        _lastWicko = wicko;

                        OnDataConsolidated(wicko);

                        OpenOn = CloseOn;
                        OpenRate = limit;
                        HighRate = limit;

                        // Reset volume accumulation for next bar
                        _accumulatedVolume = volume; // Keep current volume for potential next bars

                        Falling(data, volume);
                    }
                }
            }
        }

        /// <summary>
        /// Scans this consolidator to see if it should emit a bar due to time passing
        /// </summary>
        /// <param name="currentLocalTime">The current time in the local time zone (same as <see cref="BaseData.Time"/>)</param>
        public void Scan(DateTime currentLocalTime)
        {
        }

        /// <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
        /// <filterpriority>2</filterpriority>
        public void Dispose()
        {
            DataConsolidated = null;
            _dataConsolidatedHandler = null;
        }

        /// <summary>
        /// Resets the consolidator
        /// </summary>
        public void Reset()
        {
            _firstTick = true;
            _lastWicko = null;
            _currentBar = null;
            _consolidated = null;
            _accumulatedVolume = 0;
            CloseOn = default;
            CloseRate = default;
            HighRate = default;
            LowRate = default;
            OpenOn = default;
            OpenRate = default;
        }

        /// <summary>
        /// Event invocator for the DataConsolidated event. This should be invoked
        /// by derived classes when they have consolidated a new piece of data.
        /// </summary>
        /// <param name="consolidated">The newly consolidated data</param>
        protected void OnDataConsolidated(RenkoBar consolidated)
        {
            DataConsolidated?.Invoke(this, consolidated);
            _currentBar = consolidated;
            _dataConsolidatedHandler?.Invoke(this, consolidated);
            Consolidated = consolidated;
        }

        private void Rising(IBaseData data, decimal currentVolume)
        {
            decimal limit;

            while (CloseRate > (limit = OpenRate + BarSize))
            {
                var wicko = new RenkoBar(data.Symbol, OpenOn, CloseOn, BarSize, OpenRate, limit, LowRate, limit);
                wicko.Volume = _accumulatedVolume;

                _lastWicko = wicko;

                OnDataConsolidated(wicko);

                OpenOn = CloseOn;
                OpenRate = limit;
                LowRate = limit;

                // Reset volume accumulation for next bar - keep current volume for potential next bars
                _accumulatedVolume = currentVolume;
            }
        }

        private void Falling(IBaseData data, decimal currentVolume)
        {
            decimal limit;

            while (CloseRate < (limit = OpenRate - BarSize))
            {
                var wicko = new RenkoBar(data.Symbol, OpenOn, CloseOn, BarSize, OpenRate, HighRate, limit, limit);
                wicko.Volume = _accumulatedVolume;

                _lastWicko = wicko;

                OnDataConsolidated(wicko);

                OpenOn = CloseOn;
                OpenRate = limit;
                HighRate = limit;

                // Reset volume accumulation for next bar - keep current volume for potential next bars
                _accumulatedVolume = currentVolume;
            }
        }

        /// <summary>
        /// Gets the closest BarSize-Multiple to the price.
        /// </summary>
        /// <remarks>Based on: The Art of Computer Programming, Vol I, pag 39. Donald E. Knuth</remarks>
        /// <param name="price">Price to be rounded to the closest BarSize-Multiple</param>
        /// <param name="barSize">The size of the Renko bar</param>
        /// <returns>The closest BarSize-Multiple to the price</returns>
        public static decimal GetClosestMultiple(decimal price, decimal barSize)
        {
            if (barSize <= 0)
            {
                throw new ArgumentException("BarSize must be strictly greater than zero");
            }

            var modulus = price - barSize * Math.Floor(price / barSize);
            var round = Math.Round(modulus / barSize);
            return barSize * (Math.Floor(price / barSize) + round);
        }
    }

    /// <summary>
    /// This consolidator extends VolumeAwareRenkoConsolidator to allow dynamic updating of the bar size during runtime.
    /// It maintains the same volume-aware behavior but adds the ability to modify the bar size without creating a new consolidator.
    /// </summary>
    public class DynamicVolumeAwareRenkoConsolidator : VolumeAwareRenkoConsolidator
    {
        /// <summary>
        /// Gets or sets the target bar size for Renko bars produced by this consolidator.
        /// When setting a new value, the internal BarSize property is updated.
        /// The new bar size will take effect for future bars and calculations.
        /// </summary>
        public decimal TargetBarSize
        {
            get => BarSize;
            set
            {
                if (value <= 0)
                {
                    throw new ArgumentException("Renko consolidator BarSize must be strictly greater than zero");
                }

                // Update the base class's protected BarSize field
                BarSize = value;

                // Note: Any currently in-progress brick calculations will use the new bar size
                // from this point forward
            }
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicVolumeAwareRenkoConsolidator"/> class using the specified <paramref name="barSize"/>.
        /// </summary>
        /// <param name="barSize">The initial bar size to use for this consolidator</param>
        public DynamicVolumeAwareRenkoConsolidator(decimal barSize)
            : base(barSize)
        {
            // No custom event handling needed - use the standard VolumeAwareRenkoConsolidator events
        }
    }
}
