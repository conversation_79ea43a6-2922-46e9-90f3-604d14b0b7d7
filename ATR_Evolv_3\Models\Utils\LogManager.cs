#region imports
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Globalization;
using System.Drawing;
using QuantConnect;
using QuantConnect.Algorithm.Framework;
using QuantConnect.Algorithm.Framework.Selection;
using QuantConnect.Algorithm.Framework.Alphas;
using QuantConnect.Algorithm.Framework.Portfolio;
using QuantConnect.Algorithm.Framework.Portfolio.SignalExports;
using QuantConnect.Algorithm.Framework.Execution;
using QuantConnect.Algorithm.Framework.Risk;
using QuantConnect.Algorithm.Selection;
using QuantConnect.Api;
using QuantConnect.Parameters;
using QuantConnect.Benchmarks;
using QuantConnect.Brokerages;
using QuantConnect.Commands;
using QuantConnect.Configuration;
using QuantConnect.Util;
using QuantConnect.Interfaces;
using QuantConnect.Algorithm;
using QuantConnect.Indicators;
using QuantConnect.Data;
using QuantConnect.Data.Auxiliary;
using QuantConnect.Data.Consolidators;
using QuantConnect.Data.Custom;
using QuantConnect.Data.Custom.IconicTypes;
using QuantConnect.DataSource;
using QuantConnect.Data.Fundamental;
using QuantConnect.Data.Market;
using QuantConnect.Data.Shortable;
using QuantConnect.Data.UniverseSelection;
using QuantConnect.Notifications;
using QuantConnect.Orders;
using QuantConnect.Orders.Fees;
using QuantConnect.Orders.Fills;
using QuantConnect.Orders.OptionExercise;
using QuantConnect.Orders.Slippage;
using QuantConnect.Orders.TimeInForces;
using QuantConnect.Python;
using QuantConnect.Scheduling;
using QuantConnect.Securities;
using QuantConnect.Securities.Equity;
using QuantConnect.Securities.Future;
using QuantConnect.Securities.Option;
using QuantConnect.Securities.Positions;
using QuantConnect.Securities.Forex;
using QuantConnect.Securities.Crypto;
using QuantConnect.Securities.CryptoFuture;
using QuantConnect.Securities.IndexOption;
using QuantConnect.Securities.Interfaces;
using QuantConnect.Securities.Volatility;
using QuantConnect.Storage;
using QuantConnect.Statistics;
using QCAlgorithmFramework = QuantConnect.Algorithm.QCAlgorithm;
using QCAlgorithmFrameworkBridge = QuantConnect.Algorithm.QCAlgorithm;
using Calendar = QuantConnect.Data.Consolidators.Calendar;
#endregion

namespace QuantConnect.Algorithm.CSharp
{
    /// <summary>
    /// Manages logging with time-based windows to control verbosity.
    /// Uses a static pattern with an Initialize method.
    /// </summary>
    public static class LogManager // Made static
    {
        private static QCAlgorithm _algorithmInstance; // Static algorithm instance
        private static readonly List<LogWindow> _logWindows = new List<LogWindow>();
        // Message throttling to prevent repeated logs
        private static readonly Dictionary<string, DateTime> _lastLoggedMessages = new Dictionary<string, DateTime>();
        private static TimeSpan _defaultThrottleInterval = TimeSpan.FromMinutes(15); // Default to consolidation period
        private static int _maxLogEntries = 1000; // Maximum number of entries to keep in memory
        private static DateTime _lastCleanupTime = DateTime.MinValue; // Track when we last cleaned up logs
        private static readonly TimeSpan _cleanupInterval = TimeSpan.FromHours(1); // Clean up logs every hour
        private static bool _isLoggingEnabled = true; // Global toggle to enable/disable all logging

        /// <summary>
        /// Initializes the static LogManager with the QCAlgorithm instance and configuration.
        /// Must be called once before using logging methods.
        /// </summary>
        /// <param name="algorithm">The QCAlgorithm instance.</param>
        /// <param name="logWindows">A list of tuples representing the start and end times for logging windows.</param>
        /// <param name="throttleInterval">The minimum time between identical log messages (default: 15 minutes)</param>
        /// <param name="maxLogEntries">Maximum number of log entries to keep in memory (default: 1000)</param>
        /// <param name="isLoggingEnabled">Whether logging is enabled globally (default: true)</param>
        public static void Initialize(QCAlgorithm algorithm, List<Tuple<DateTime, DateTime>> logWindows = null,
                                      TimeSpan? throttleInterval = null, int maxLogEntries = 1000)
        {
            if (_algorithmInstance != null)
            {
                _algorithmInstance.Error("LogManager.Initialize called more than once.");
                return; // Avoid re-initialization
            }

            _algorithmInstance = algorithm ?? throw new ArgumentNullException(nameof(algorithm), "Algorithm instance cannot be null for LogManager.");

            if (throttleInterval.HasValue)
            {
                _defaultThrottleInterval = throttleInterval.Value;
            }

            if (maxLogEntries > 0)
            {
                _maxLogEntries = maxLogEntries;
            }

            if (logWindows != null)
            {
                _logWindows.Clear(); // Clear any previous windows if re-initializing (though discouraged)
                foreach (var logWindow in logWindows)
                {
                    _logWindows.Add(new LogWindow { Start = logWindow.Item1, End = logWindow.Item2 });
                    // Use LogAlways directly now, assuming Initialize might be called before first log
                    _algorithmInstance.Log($"LogManager: Added log window: {logWindow.Item1:yyyy-MM-dd HH:mm:ss} to {logWindow.Item2:yyyy-MM-dd HH:mm:ss}");
                }
            }
            _algorithmInstance.Log("LogManager Initialized.");
        }


        /// <summary>
        /// Enables or disables all logging globally.
        /// </summary>
        /// <param name="enabled">Whether logging should be enabled.</param>
        public static void SetLoggingEnabled(bool enabled)
        {
            _isLoggingEnabled = enabled;
            if (enabled)
            {
                // Use LogAlways to ensure this message gets through regardless of windows/throttling
                LogAlways("LogManager: Logging enabled");
            }
            else
            {
                LogAlways("LogManager: Logging disabled");
            }
        }

        /// <summary>
        /// Gets the current global logging status.
        /// </summary>
        /// <returns>True if logging is enabled, false otherwise.</returns>
        public static bool IsLoggingEnabled()
        {
            return _isLoggingEnabled;
        }

        /// <summary>
        /// Adds a time window where verbose logging should be enabled.
        /// </summary>
        /// <param name="start">The start time of the log window.</param>
        /// <param name="end">The end time of the log window.</param>
        public static void AddLogWindow(DateTime start, DateTime end)
        {
            _logWindows.Add(new LogWindow { Start = start, End = end });
            LogAlways($"LogManager: Added log window: {start:yyyy-MM-dd HH:mm:ss} to {end:yyyy-MM-dd HH:mm:ss}");
        }

        /// <summary>
        /// Logs a message if the current time is within a log window and logging is globally enabled.
        /// </summary>
        /// <param name="message">The message to log.</param>
        public static void Log(string message)
        {
            // Check initialization
            if (_algorithmInstance == null) { Console.WriteLine($"ERROR: LogManager not initialized. Message: {message}"); return; }

            // Skip logging if globally disabled
            if (!_isLoggingEnabled) return;

            // Check if we need to clean up old log entries
            CheckAndCleanupOldLogs();

            if (IsInLogWindow())
            {
                _algorithmInstance.Log(message);
            }
        }

        /// <summary>
        /// Logs a message if the current time is within a log window and it hasn't been logged recently
        /// with the specified throttle interval, and logging is globally enabled.
        /// </summary>
        /// <param name="message">The message to log.</param>
        /// <param name="throttleInterval">The minimum time span between logging the same message.</param>
        public static void LogThrottled(string message, TimeSpan throttleInterval)
        {
            // Check initialization
            if (_algorithmInstance == null) { Console.WriteLine($"ERROR: LogManager not initialized. Message: {message}"); return; }

            // Skip logging if globally disabled
            if (!_isLoggingEnabled) return;

            // Check if we need to clean up old log entries
            CheckAndCleanupOldLogs();

            if (IsInLogWindow() && ShouldLogMessage(message, throttleInterval))
            {
                _algorithmInstance.Log(message);
            }
        }

        /// <summary>
        /// Logs a message regardless of the time window, but still respects the global logging toggle.
        /// Requires LogManager to be initialized.
        /// </summary>
        /// <param name="message">The message to log.</param>
        public static void LogAlways(string message)
        {
            // Check initialization
            if (_algorithmInstance == null) { Console.WriteLine($"ERROR: LogManager not initialized. Message: {message}"); return; }

            if (_isLoggingEnabled)
            {
                _algorithmInstance.Log(message);
            }
        }

        /// <summary>
        /// Logs a message only if it's different from the last logged message with the same prefix.
        /// Requires LogManager to be initialized.
        /// </summary>
        /// <param name="prefix">The prefix to identify the message type.</param>
        /// <param name="message">The complete message to log.</param>
        public static void LogOnChange(string prefix, string message)
        {
            // Check initialization
            if (_algorithmInstance == null) { Console.WriteLine($"ERROR: LogManager not initialized. Message: {message}"); return; }

            // Skip logging if globally disabled
            if (!_isLoggingEnabled) return;

            // Check if we need to clean up old log entries
            CheckAndCleanupOldLogs();

            string key = $"CHANGE_{prefix}";
            // Use _algorithmInstance.Time
            if (!_lastLoggedMessages.ContainsKey(key) || _lastLoggedMessages[key] != _algorithmInstance.Time)
            {
                // Check content change - Note: This comparison might be fragile if ToString() changes format.
                // Consider storing the actual message string if robust change detection is critical.
                if (_lastLoggedMessages.TryGetValue(key, out DateTime lastLogTime))
                {
                    // Simple check: if last log time matches current time, assume it's the same log cycle.
                    // A more robust check might involve storing the previous message string itself.
                    if (lastLogTime == _algorithmInstance.Time)
                    {
                        // Potentially the same message within the same time slice, requires careful check
                        // Let's log for now, can be refined if needed.
                        // To be safe, let's re-evaluate if this check is truly needed or how to implement robustly.
                        // For now, removing the content check part:
                        // if (_lastLoggedMessages[key].ToString() == message) // This comparison is removed
                        // {
                        //     // Message content hasn't changed, don't log
                        //     return;
                        // }
                    }
                }


                if (IsInLogWindow())
                {
                    _algorithmInstance.Log(message);
                    _lastLoggedMessages[key] = _algorithmInstance.Time; // Store current time
                }
            }
        }

        /// <summary>
        /// Checks if it's time to clean up old log entries and performs the cleanup if needed
        /// </summary>
        private static void CheckAndCleanupOldLogs() // Made static
        {
            // Check initialization
            if (_algorithmInstance == null) return; // Cannot clean up without time reference

            if (_lastLoggedMessages.Count > _maxLogEntries ||
                (_algorithmInstance.Time - _lastCleanupTime) > _cleanupInterval)
            {
                CleanupOldLogs();
                _lastCleanupTime = _algorithmInstance.Time;
            }
        }

        /// <summary>
        /// Cleans up old log entries to prevent memory buildup
        /// </summary>
        private static void CleanupOldLogs() // Made static
        {
            // Check initialization
            if (_algorithmInstance == null) return; // Cannot clean up without time reference

            if (_lastLoggedMessages.Count == 0) return;

            // If dictionary size is larger than max entries, remove the oldest entries
            if (_lastLoggedMessages.Count > _maxLogEntries)
            {
                // Sort by timestamp (oldest first)
                var oldestEntries = _lastLoggedMessages
                    .OrderBy(x => x.Value)
                    .Take(_lastLoggedMessages.Count - _maxLogEntries / 2) // Remove half, keeping the most recent
                    .Select(x => x.Key)
                    .ToList();

                foreach (var key in oldestEntries)
                {
                    _lastLoggedMessages.Remove(key);
                }
            }

            // Additionally, remove any entries older than the cleanup interval
            DateTime cutoffTime = _algorithmInstance.Time - _cleanupInterval;
            var expiredEntries = _lastLoggedMessages
                .Where(x => x.Value < cutoffTime)
                .Select(x => x.Key)
                .ToList();

            foreach (var key in expiredEntries)
            {
                _lastLoggedMessages.Remove(key);
            }
        }

        /// <summary>
        /// Checks if current time is within any defined log window.
        /// </summary>
        /// <returns>True if the current time is within a log window, false otherwise.</returns>
        private static bool IsInLogWindow() // Made static
        {
            // Check initialization
            if (_algorithmInstance == null) return false; // Assume false if not initialized

            if (_logWindows.Count == 0)
                return true; // If no windows defined, always log

            var currentTime = _algorithmInstance.Time;
            return _logWindows.Any(window => currentTime >= window.Start && currentTime <= window.End);
        }

        /// <summary>
        /// Determines if a message should be logged based on throttling rules.
        /// </summary>
        /// <param name="message">The message to check.</param>
        /// <param name="throttleInterval">Optional custom throttle interval.</param>
        /// <returns>True if the message should be logged, false otherwise.</returns>
        private static bool ShouldLogMessage(string message, TimeSpan? throttleInterval = null) // Made static
        {
            // Check initialization
            if (_algorithmInstance == null) return false; // Cannot log if not initialized

            var interval = throttleInterval ?? _defaultThrottleInterval;

            // Use the complete message as the key for exact matching
            // Limit the key length to prevent excessive memory usage with very long messages
            string messageKey = message.Length > 20 ? message[..20] : message;

            // Check if we've logged this exact message recently
            if (_lastLoggedMessages.TryGetValue(messageKey, out DateTime lastTime))
            {
                // If not enough time has passed, skip logging
                if (_algorithmInstance.Time - lastTime < interval)
                {
                    return false;
                }
            }

            // Update the last logged time for this message
            _lastLoggedMessages[messageKey] = _algorithmInstance.Time;
            return true;
        }

        /// <summary>
        /// Represents a time window for logging.
        /// </summary>
        private class LogWindow // Removed static modifier
        {
            public DateTime Start { get; set; }
            public DateTime End { get; set; }
        }
    }
}
