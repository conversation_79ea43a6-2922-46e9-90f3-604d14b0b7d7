/*
 * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
 * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
*/

using System;
using QuantConnect.Data;
using QuantConnect.Data.Consolidators;
using QuantConnect.Data.Market;

namespace QuantConnect.Algorithm.CSharp
{
    /// <summary>
    /// Custom DebugSequentialConsolidator with enhanced logging for debugging consolidator chain issues.
    /// This consolidator chains two consolidators together where the output of the first becomes the input of the second.
    /// </summary>
    public class DebugSequentialConsolidator : IDataConsolidator
    {
        private readonly IDataConsolidator _first;
        private readonly IDataConsolidator _second;

        /// <summary>
        /// Gets the first consolidator in the chain
        /// </summary>
        public IDataConsolidator First => _first;

        /// <summary>
        /// Gets the second consolidator in the chain
        /// </summary>
        public IDataConsolidator Second => _second;

        /// <summary>
        /// Gets the most recently consolidated piece of data. This will be null if this consolidator
        /// has not produced any data yet.
        /// </summary>
        public IBaseData Consolidated { get; private set; }

        /// <summary>
        /// Gets a clone of the data being currently consolidated
        /// </summary>
        public IBaseData WorkingData => _second.WorkingData;

        /// <summary>
        /// Gets the type consumed by this consolidator
        /// </summary>
        public Type InputType => _first.InputType;

        /// <summary>
        /// Gets the type produced by this consolidator
        /// </summary>
        public Type OutputType => _second.OutputType;

        /// <summary>
        /// Event handler that fires when a new piece of data is produced
        /// </summary>
        public event DataConsolidatedHandler DataConsolidated;

        /// <summary>
        /// Initializes a new instance of the <see cref="DebugSequentialConsolidator"/> class
        /// </summary>
        /// <param name="first">The first consolidator to receive data</param>
        /// <param name="second">The second consolidator to receive data from the first</param>
        public DebugSequentialConsolidator(IDataConsolidator first, IDataConsolidator second)
        {
            _first = first ?? throw new ArgumentNullException(nameof(first));
            _second = second ?? throw new ArgumentNullException(nameof(second));

            // LogManager.Log($"[DEBUG-SEQUENTIAL-INIT] Creating DebugSequentialConsolidator: {_first.GetType().Name} -> {_second.GetType().Name}");
            // LogManager.Log($"[DEBUG-SEQUENTIAL-INIT] Input Type: {InputType.Name}, Output Type: {OutputType.Name}");

            // Wire up the first consolidator to feed the second
            _first.DataConsolidated += OnFirstConsolidatorDataConsolidated;
            
            // Wire up the second consolidator to fire our event
            _second.DataConsolidated += OnSecondConsolidatorDataConsolidated;
        }

        /// <summary>
        /// Updates this consolidator with the specified data
        /// </summary>
        /// <param name="data">The new data for the consolidator</param>
        public void Update(IBaseData data)
        {
            // LogManager.LogThrottled($"[DEBUG-SEQUENTIAL-INPUT] DebugSequentialConsolidator.Update called with {data?.GetType().Name} for {data?.Symbol} at {data?.Time}", TimeSpan.FromMinutes(5));
            // LogManager.LogThrottled($"[DEBUG-SEQUENTIAL-INPUT] Data details: Close={GetClosePrice(data):F2}, Volume={GetVolume(data):F0}", TimeSpan.FromMinutes(5));
            
            // Feed data to the first consolidator
            _first.Update(data);
        }

        /// <summary>
        /// Scans this consolidator to see if it should emit a bar due to time passing
        /// </summary>
        /// <param name="currentLocalTime">The current time in the local time zone (same as <see cref="BaseData.Time"/>)</param>
        public void Scan(DateTime currentLocalTime)
        {
            _first.Scan(currentLocalTime);
            _second.Scan(currentLocalTime);
        }

        /// <summary>
        /// Resets this consolidator to its initial state
        /// </summary>
        public void Reset()
        {
            _first.Reset();
            _second.Reset();
            Consolidated = null;
        }

        /// <summary>
        /// Event handler for when the first consolidator produces data
        /// </summary>
        private void OnFirstConsolidatorDataConsolidated(object sender, IBaseData consolidated)
        {
            // LogManager.Log($"[DEBUG-SEQUENTIAL-FIRST] First consolidator ({_first.GetType().Name}) produced data: {consolidated?.GetType().Name} for {consolidated?.Symbol} at {consolidated?.Time}");
            // LogManager.Log($"[DEBUG-SEQUENTIAL-FIRST] First output details: Close={GetClosePrice(consolidated):F2}, Volume={GetVolume(consolidated):F0}");
            // LogManager.Log($"[DEBUG-SEQUENTIAL-FIRST] Feeding to second consolidator ({_second.GetType().Name})...");
            
            // Feed the output of the first consolidator to the second
            _second.Update(consolidated);
        }

        /// <summary>
        /// Event handler for when the second consolidator produces data
        /// </summary>
        private void OnSecondConsolidatorDataConsolidated(object sender, IBaseData consolidated)
        {
            // LogManager.Log($"[DEBUG-SEQUENTIAL-SECOND] Second consolidator ({_second.GetType().Name}) produced data: {consolidated?.GetType().Name} for {consolidated?.Symbol} at {consolidated?.Time}");
            // LogManager.Log($"[DEBUG-SEQUENTIAL-SECOND] Final output details: Close={GetClosePrice(consolidated):F2}, Volume={GetVolume(consolidated):F0}");
            // LogManager.Log($"[DEBUG-SEQUENTIAL-SUCCESS] DebugSequentialConsolidator chain completed successfully! Firing DataConsolidated event...");
            
            Consolidated = consolidated;
            DataConsolidated?.Invoke(this, consolidated);
        }


        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            // LogManager.Log($"[DEBUG-SEQUENTIAL-DISPOSE] Disposing DebugSequentialConsolidator: {_first.GetType().Name} -> {_second.GetType().Name}");
            
            _first.DataConsolidated -= OnFirstConsolidatorDataConsolidated;
            _second.DataConsolidated -= OnSecondConsolidatorDataConsolidated;
            
            _first?.Dispose();
            _second?.Dispose();
        }
    }
}
