#region imports
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Linq;
    using System.Globalization;
    using System.Drawing;
    using QuantConnect;
    using QuantConnect.Algorithm.Framework;
    using QuantConnect.Algorithm.Framework.Selection;
    using QuantConnect.Algorithm.Framework.Alphas;
    using QuantConnect.Algorithm.Framework.Portfolio;
    using QuantConnect.Algorithm.Framework.Portfolio.SignalExports;
    using QuantConnect.Algorithm.Framework.Execution;
    using QuantConnect.Algorithm.Framework.Risk;
    using QuantConnect.Algorithm.Selection;
    using QuantConnect.Api;
    using QuantConnect.Parameters;
    using QuantConnect.Benchmarks;
    using QuantConnect.Brokerages;
    using QuantConnect.Commands;
    using QuantConnect.Configuration;
    using QuantConnect.Util;
    using QuantConnect.Interfaces;
    using QuantConnect.Algorithm;
    using QuantConnect.Indicators;
    using QuantConnect.Data;
    using QuantConnect.Data.Auxiliary;
    using QuantConnect.Data.Consolidators;
    using QuantConnect.Data.Custom;
    using QuantConnect.Data.Custom.IconicTypes;
    using QuantConnect.DataSource;
    using QuantConnect.Data.Fundamental;
    using QuantConnect.Data.Market;
    using QuantConnect.Data.Shortable;
    using QuantConnect.Data.UniverseSelection;
    using QuantConnect.Notifications;
    using QuantConnect.Orders;
    using QuantConnect.Orders.Fees;
    using QuantConnect.Orders.Fills;
    using QuantConnect.Orders.OptionExercise;
    using QuantConnect.Orders.Slippage;
    using QuantConnect.Orders.TimeInForces;
    using QuantConnect.Python;
    using QuantConnect.Scheduling;
    using QuantConnect.Securities;
    using QuantConnect.Securities.Equity;
    using QuantConnect.Securities.Future;
    using QuantConnect.Securities.Option;
    using QuantConnect.Securities.Positions;
    using QuantConnect.Securities.Forex;
    using QuantConnect.Securities.Crypto;
    using QuantConnect.Securities.CryptoFuture;
    using QuantConnect.Securities.IndexOption;
    using QuantConnect.Securities.Interfaces;
    using QuantConnect.Securities.Volatility;
    using QuantConnect.Storage;
    using QuantConnect.Statistics;
    using QCAlgorithmFramework = QuantConnect.Algorithm.QCAlgorithm;
    using QCAlgorithmFrameworkBridge = QuantConnect.Algorithm.QCAlgorithm;
    using Calendar = QuantConnect.Data.Consolidators.Calendar;
    using QuantConnect.Indicators.CandlestickPatterns;
    using QLNet;
    using System.Threading.Tasks;
    using System.Collections.Concurrent;
    using System.Text;
#endregion

namespace QuantConnect.Algorithm.CSharp
{
    /// <summary>
    /// A specialized DollarVolumeConsolidator that can handle RenkoBar input from DynamicRenkoConsolidator.
    /// This consolidator automatically adjusts its target when receiving RenkoBar data to ensure proper consolidation.
    /// </summary>
    public class RenkoCompatibleDollarVolumeConsolidator : DollarVolumeConsolidator
    {
        private readonly decimal _originalTarget;
        private readonly decimal _renkoAdjustmentFactor;
        private bool _hasAdjustedForRenko = false;

        /// <summary>
        /// Initializes a new instance of RenkoCompatibleDollarVolumeConsolidator.
        /// </summary>
        /// <param name="barSize">The dollar volume size for regular TradeBars</param>
        /// <param name="renkoAdjustmentFactor">Factor to multiply target when receiving RenkoBars (default: 0.01 = 1%)</param>
        public RenkoCompatibleDollarVolumeConsolidator(decimal barSize, decimal renkoAdjustmentFactor = 0.01m) : base(barSize)
        {
            _originalTarget = barSize;
            _renkoAdjustmentFactor = renkoAdjustmentFactor;
            // LogManager.Log($"[RENKO-COMPATIBLE] Initialized with originalTarget={_originalTarget:F0}, adjustmentFactor={_renkoAdjustmentFactor}");
        }

        /// <summary>
        /// Updates this consolidator with the specified data.
        /// Automatically adjusts target when receiving RenkoBar data.
        /// </summary>
        /// <param name="data">The new data for the consolidator</param>
        public override void Update(BaseData data)
        {
            // Check if we're receiving RenkoBar data and need to adjust
            if (!_hasAdjustedForRenko && data is RenkoBar)
            {
                decimal newTarget = _originalTarget * _renkoAdjustmentFactor;
                // LogManager.Log($"[RENKO-COMPATIBLE] Detected RenkoBar input! Adjusting target from {TargetBarSize:F0} to {newTarget:F0} (factor: {_renkoAdjustmentFactor})");
                TargetBarSize = newTarget;
                _hasAdjustedForRenko = true;
            }

            // Log the data type for debugging (throttled)
            // LogManager.LogThrottled($"[RENKO-COMPATIBLE] Processing {data?.GetType().Name} for {data?.Symbol} with target {TargetBarSize:F0}", TimeSpan.FromMinutes(10));

            // Call the base implementation
            base.Update(data);
        }
    }
}
