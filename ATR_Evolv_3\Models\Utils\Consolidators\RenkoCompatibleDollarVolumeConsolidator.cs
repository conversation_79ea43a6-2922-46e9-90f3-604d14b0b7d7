#region imports
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Linq;
    using System.Globalization;
    using System.Drawing;
    using QuantConnect;
    using QuantConnect.Algorithm.Framework;
    using QuantConnect.Algorithm.Framework.Selection;
    using QuantConnect.Algorithm.Framework.Alphas;
    using QuantConnect.Algorithm.Framework.Portfolio;
    using QuantConnect.Algorithm.Framework.Portfolio.SignalExports;
    using QuantConnect.Algorithm.Framework.Execution;
    using QuantConnect.Algorithm.Framework.Risk;
    using QuantConnect.Algorithm.Selection;
    using QuantConnect.Api;
    using QuantConnect.Parameters;
    using QuantConnect.Benchmarks;
    using QuantConnect.Brokerages;
    using QuantConnect.Commands;
    using QuantConnect.Configuration;
    using QuantConnect.Util;
    using QuantConnect.Interfaces;
    using QuantConnect.Algorithm;
    using QuantConnect.Indicators;
    using QuantConnect.Data;
    using QuantConnect.Data.Auxiliary;
    using QuantConnect.Data.Consolidators;
    using QuantConnect.Data.Custom;
    using QuantConnect.Data.Custom.IconicTypes;
    using QuantConnect.DataSource;
    using QuantConnect.Data.Fundamental;
    using QuantConnect.Data.Market;
    using QuantConnect.Data.Shortable;
    using QuantConnect.Data.UniverseSelection;
    using QuantConnect.Notifications;
    using QuantConnect.Orders;
    using QuantConnect.Orders.Fees;
    using QuantConnect.Orders.Fills;
    using QuantConnect.Orders.OptionExercise;
    using QuantConnect.Orders.Slippage;
    using QuantConnect.Orders.TimeInForces;
    using QuantConnect.Python;
    using QuantConnect.Scheduling;
    using QuantConnect.Securities;
    using QuantConnect.Securities.Equity;
    using QuantConnect.Securities.Future;
    using QuantConnect.Securities.Option;
    using QuantConnect.Securities.Positions;
    using QuantConnect.Securities.Forex;
    using QuantConnect.Securities.Crypto;
    using QuantConnect.Securities.CryptoFuture;
    using QuantConnect.Securities.IndexOption;
    using QuantConnect.Securities.Interfaces;
    using QuantConnect.Securities.Volatility;
    using QuantConnect.Storage;
    using QuantConnect.Statistics;
    using QCAlgorithmFramework = QuantConnect.Algorithm.QCAlgorithm;
    using QCAlgorithmFrameworkBridge = QuantConnect.Algorithm.QCAlgorithm;
    using Calendar = QuantConnect.Data.Consolidators.Calendar;
    using QuantConnect.Indicators.CandlestickPatterns;
    using QLNet;
    using System.Threading.Tasks;
    using System.Collections.Concurrent;
    using System.Text;
#endregion

namespace QuantConnect.Algorithm.CSharp
{
    /// <summary>
    /// A specialized DollarVolumeConsolidator that uses a constant very small target to ensure consolidation always triggers.
    /// This solves the RenkoBar compatibility issue by making the target so small that any data will trigger consolidation.
    /// </summary>
    public class RenkoCompatibleDollarVolumeConsolidator : DollarVolumeConsolidator
    {
        private const decimal CONSTANT_SMALL_TARGET = 1000m; // $1000 - very small, will always trigger

        /// <summary>
        /// Initializes a new instance of RenkoCompatibleDollarVolumeConsolidator.
        /// Uses a constant small target regardless of input parameters.
        /// </summary>
        /// <param name="barSize">Ignored - uses constant small target instead</param>
        /// <param name="renkoAdjustmentFactor">Ignored - uses constant small target instead</param>
        public RenkoCompatibleDollarVolumeConsolidator(decimal barSize, decimal renkoAdjustmentFactor = 0.0001m) : base(CONSTANT_SMALL_TARGET)
        {
            // Always use the constant small target, ignore input parameters
        }

        /// <summary>
        /// Gets or sets the target bar size. Always returns the constant small value.
        /// Setting this property does nothing - the target remains constant.
        /// </summary>
        public new decimal TargetBarSize
        {
            get => CONSTANT_SMALL_TARGET;
            set { /* Do nothing - disable adjustment */ }
        }
    }
}
